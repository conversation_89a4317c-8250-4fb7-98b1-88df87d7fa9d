
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named pyimod03_importers - imported by D:\anaconda3\envs\dabao\lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (top-level), D:\anaconda3\envs\dabao\lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (top-level)
missing module named 'org.python' - imported by copy (optional), xml.sax (delayed, conditional), setuptools.sandbox (conditional)
missing module named urllib.pathname2url - imported by urllib (conditional), PyInstaller.lib.modulegraph._compat (conditional)
excluded module named unittest - imported by doctest (top-level)
missing module named pwd - imported by posixpath (delayed, conditional), shutil (optional), tarfile (optional), http.server (delayed, optional), webbrowser (delayed), pathlib (delayed, conditional, optional), distutils.util (delayed, conditional, optional), distutils.archive_util (optional), netrc (delayed, conditional), getpass (delayed)
missing module named grp - imported by shutil (optional), tarfile (optional), pathlib (delayed), distutils.archive_util (optional)
missing module named termios - imported by tty (top-level), getpass (optional)
missing module named _posixsubprocess - imported by subprocess (conditional)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), PyInstaller.loader.pyimod02_archive (delayed)
missing module named readline - imported by cmd (delayed, conditional, optional), code (delayed, conditional, optional), pdb (delayed, optional)
missing module named org - imported by pickle (optional)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), pkg_resources._vendor.packaging._manylinux (delayed, optional)
missing module named 'pkg_resources.extern.pyparsing' - imported by pkg_resources._vendor.packaging.markers (top-level), pkg_resources._vendor.packaging.requirements (top-level)
missing module named 'pkg_resources.extern.more_itertools' - imported by pkg_resources._vendor.jaraco.functools (top-level)
missing module named 'pkg_resources.extern.importlib_resources' - imported by pkg_resources._vendor.jaraco.text (optional)
missing module named 'typing.io' - imported by importlib.resources (top-level)
missing module named 'win32com.shell' - imported by pkg_resources._vendor.appdirs (conditional, optional)
missing module named 'com.sun' - imported by pkg_resources._vendor.appdirs (delayed, conditional, optional)
missing module named com - imported by pkg_resources._vendor.appdirs (delayed)
missing module named win32api - imported by distutils.msvccompiler (optional), pkg_resources._vendor.appdirs (delayed, conditional, optional)
missing module named win32com - imported by pkg_resources._vendor.appdirs (delayed)
missing module named _winreg - imported by platform (delayed, optional), pkg_resources._vendor.appdirs (delayed, conditional)
missing module named jinja2 - imported by pkg_resources._vendor.pyparsing.diagram (top-level), setuptools._vendor.pyparsing.diagram (top-level)
missing module named pyparsing - imported by pkg_resources._vendor.pyparsing.diagram (top-level), setuptools._vendor.pyparsing.diagram (top-level)
missing module named railroad - imported by pkg_resources._vendor.pyparsing.diagram (top-level), setuptools._vendor.pyparsing.diagram (top-level)
missing module named pkg_resources.extern.packaging - imported by pkg_resources.extern (top-level), pkg_resources (top-level)
missing module named pkg_resources.extern.appdirs - imported by pkg_resources.extern (top-level), pkg_resources (top-level)
missing module named 'pkg_resources.extern.jaraco' - imported by pkg_resources (top-level), pkg_resources._vendor.jaraco.text (top-level)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named 'java.lang' - imported by platform (delayed, optional), xml.sax._exceptions (conditional)
missing module named tests - imported by openpyxl.reader.excel (optional)
missing module named 'defusedxml.ElementTree' - imported by openpyxl.xml.functions (conditional)
missing module named 'lxml.etree' - imported by openpyxl.xml.functions (conditional), pandas.io.xml (delayed), pandas.io.formats.xml (delayed), pandas.io.html (delayed)
missing module named PIL - imported by PyInstaller.building.splash (optional), openpyxl.drawing.image (optional)
missing module named defusedxml - imported by openpyxl.xml (delayed, optional)
missing module named lxml - imported by openpyxl.xml (delayed, optional)
missing module named StringIO - imported by PyInstaller.lib.modulegraph._compat (conditional), PyInstaller.lib.modulegraph.zipio (conditional), six (conditional)
excluded module named numpy.testing - imported by numpy.f2py.f2py_testing (top-level), numpy._pytesttester (delayed, conditional), numpy (delayed, conditional)
missing module named numpy.core.linspace - imported by numpy.core (top-level), numpy.lib.index_tricks (top-level)
missing module named numpy.core.iinfo - imported by numpy.core (top-level), numpy.lib.twodim_base (top-level)
missing module named numpy.core.transpose - imported by numpy.core (top-level), numpy.lib.function_base (top-level)
missing module named numpy.core.asarray - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.utils (top-level), numpy.fft._pocketfft (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.empty - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.integer - imported by numpy.core (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.sqrt - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.conjugate - imported by numpy.core (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.swapaxes - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.zeros - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.sort - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.argsort - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sign - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.isnan - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.count_nonzero - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.divide - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.matmul - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.object_ - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.asanyarray - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.intp - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.atleast_2d - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.product - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.amax - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.amin - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.moveaxis - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.geterrobj - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.errstate - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.finfo - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.isfinite - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sum - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.fastCopyAndTranspose - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.multiply - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.add - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.dot - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.Inf - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.all - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.newaxis - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.complexfloating - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.inexact - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.cdouble - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.csingle - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.double - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.single - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.intc - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.empty_like - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.array - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.ufunc - imported by numpy.core (top-level), numpy.lib.utils (top-level)
missing module named numpy.core.ndarray - imported by numpy.core (top-level), numpy.lib.utils (top-level)
missing module named numpy.core.ones - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.hstack - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.atleast_1d - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.isscalar - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.atleast_3d - imported by numpy.core (top-level), numpy.lib.shape_base (top-level)
missing module named numpy.core.vstack - imported by numpy.core (top-level), numpy.lib.shape_base (top-level)
missing module named pickle5 - imported by numpy.compat.py3k (optional)
missing module named numpy.eye - imported by numpy (delayed), numpy.core.numeric (delayed)
missing module named numpy.recarray - imported by numpy (top-level), numpy.ma.mrecords (top-level)
missing module named numpy.dtype - imported by numpy (top-level), numpy.ma.mrecords (top-level), numpy.ctypeslib (top-level)
missing module named numpy.expand_dims - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.array - imported by numpy (top-level), numpy.ma.core (top-level), numpy.ma.extras (top-level), numpy.ma.mrecords (top-level)
missing module named numpy.bool_ - imported by numpy (top-level), numpy.ma.core (top-level), numpy.ma.mrecords (top-level)
missing module named numpy.iscomplexobj - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.amin - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.amax - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.ndarray - imported by numpy (top-level), numpy.ma.core (top-level), numpy.ma.extras (top-level), numpy.ma.mrecords (top-level), numpy.ctypeslib (top-level), pandas.compat.numpy.function (top-level)
missing module named numpy.histogramdd - imported by numpy (delayed), numpy.lib.twodim_base (delayed)
missing module named sets - imported by pytz.tzinfo (optional)
missing module named UserDict - imported by pytz.lazy (optional)
missing module named pytest - imported by pandas._testing._io (delayed), pandas._testing (delayed), pandas.util._tester (delayed, optional), pandas.tests.dtypes.cast.test_downcast (top-level), pandas.tests.frame.methods.test_values (top-level), pandas.util._test_decorators (top-level), pandas.tests.arrays.floating.test_function (top-level), pandas.tests.reshape.concat.test_series (top-level), pandas.tests.series.methods.test_drop_duplicates (top-level), pandas.tests.indexes.datetimes.test_datetime (top-level), pandas.tests.frame.methods.test_explode (top-level), pandas.tests.scalar.timestamp.test_rendering (top-level), pandas.tests.indexes.datetimes.test_indexing (top-level), pandas.tests.util.test_validate_kwargs (top-level), pandas.tests.indexes.datetimes.test_ops (top-level), pandas.tests.series.methods.test_astype (top-level), pandas.tests.arrays.categorical.test_missing (top-level), pandas.tests.reshape.merge.test_multi (top-level), pandas.tests.extension.test_period (top-level), pandas.tests.extension.base.casting (top-level), pandas.tests.extension.base.constructors (top-level), pandas.tests.extension.base.dim2 (top-level), pandas.tests.extension.base.dtype (top-level), pandas.tests.extension.base.getitem (top-level), pandas.tests.extension.base.groupby (top-level), pandas.tests.extension.base.io (top-level), pandas.tests.extension.base.methods (top-level), pandas.tests.extension.base.missing (top-level), pandas.tests.extension.base.ops (top-level), pandas.tests.extension.base.printing (top-level), pandas.tests.extension.base.reduce (top-level), pandas.tests.extension.base.reshaping (top-level), pandas.tests.extension.base.setitem (top-level), pandas.tests.indexes.interval.test_indexing (top-level), pandas.tests.series.methods.test_matmul (top-level), pandas.tests.indexing.test_check_indexer (top-level), pandas.tests.dtypes.test_inference (top-level), pandas.tests.arithmetic.test_object (top-level), pandas.tests.tools.test_to_datetime (top-level), pandas.tests.indexes.datetimes.test_timezones (top-level), pandas.tests.series.methods.test_to_csv (top-level), pandas.tests.indexes.ranges.test_constructors (top-level), pandas.tests.groupby.test_sample (top-level), pandas.tests.frame.methods.test_combine_first (top-level), pandas.tests.indexes.timedeltas.test_formats (top-level), pandas.tests.frame.methods.test_sort_index (top-level), pandas.tests.tseries.offsets.test_offsets (top-level), pandas.tests.tseries.offsets.common (top-level), pandas.tests.tslibs.test_array_to_datetime (top-level), pandas.tests.series.methods.test_tz_localize (top-level), pandas.tests.tseries.holiday.test_calendar (top-level), pandas.tests.arrays.test_datetimes (top-level), pandas.tests.groupby.test_rank (top-level), pandas.tests.indexes.ranges.test_setops (top-level), pandas.tests.series.methods.test_copy (top-level), pandas.tests.arithmetic.test_array_ops (top-level), pandas.tests.indexes.multi.test_reindex (top-level), pandas.tests.series.methods.test_dropna (top-level), pandas.tests.indexes.period.methods.test_repeat (top-level), pandas.tests.series.methods.test_view (top-level), pandas.tests.generic.test_label_or_level_utils (top-level), pandas.tests.frame.methods.test_to_dict (top-level), pandas.tests.frame.methods.test_reindex_like (top-level), pandas.tests.groupby.test_groupby_shift_diff (top-level), pandas.tests.series.methods.test_fillna (top-level), pandas.tests.indexes.numeric.test_numeric (top-level), pandas.tests.indexes.common (top-level), pandas.tests.test_expressions (top-level), pandas.tests.frame.indexing.test_insert (top-level), pandas.tests.frame.methods.test_quantile (top-level), pandas.tests.series.test_repr (top-level), pandas.tests.frame.methods.test_convert (top-level), pandas.tests.series.indexing.test_where (top-level), pandas.tests.indexes.timedeltas.test_setops (top-level), pandas.tests.frame.methods.test_tz_convert (top-level), pandas.tests.strings.test_strings (top-level), pandas.tests.dtypes.cast.test_construct_from_scalar (top-level), pandas.tests.indexes.datetimelike_.test_drop_duplicates (top-level), pandas.tests.plotting.frame.test_frame_subplots (top-level), pandas.tests.frame.indexing.test_delitem (top-level), pandas.tests.dtypes.test_missing (top-level), pandas.tests.frame.methods.test_reorder_levels (top-level), pandas.tests.groupby.test_size (top-level), pandas.tests.arrays.boolean.test_comparison (top-level), pandas.tests.arrays.string_.test_string (top-level), pandas.tests.frame.methods.test_isin (top-level), pandas.tests.reshape.merge.test_merge_asof (top-level), pandas.tests.indexes.categorical.test_constructors (top-level), pandas.tests.indexing.multiindex.test_indexing_slow (top-level), pandas.tests.resample.test_resample_api (top-level), pandas.tests.plotting.test_boxplot_method (top-level), pandas.tests.util.test_assert_index_equal (top-level), pandas.tests.indexes.multi.test_constructors (top-level), pandas.tests.arrays.categorical.test_api (top-level), pandas.tests.groupby.transform.test_numba (top-level), pandas.tests.indexes.test_numpy_compat (top-level), pandas.tests.indexes.categorical.test_astype (top-level), pandas.tests.tslibs.test_parsing (top-level), pandas.tests.frame.methods.test_duplicated (top-level), pandas.tests.indexes.datetimelike_.test_equals (top-level), pandas.tests.arrays.string_.test_string_arrow (top-level), pandas.tests.indexes.test_indexing (top-level), pandas.tests.indexes.period.methods.test_asfreq (top-level), pandas.tests.resample.test_resampler_grouper (top-level), pandas.tests.extension.test_external_block (top-level), pandas.tests.indexes.multi.test_compat (top-level), pandas.tests.arrays.boolean.test_indexing (top-level), pandas.tests.indexes.period.test_constructors (top-level), pandas.tests.indexes.numeric.test_astype (top-level), pandas.tests.scalar.timestamp.test_comparisons (top-level), pandas.tests.generic.test_frame (top-level), pandas.tests.generic.test_generic (top-level), pandas.tests.util.test_numba (top-level), pandas.tests.frame.methods.test_sort_values (top-level), pandas.tests.tslibs.test_timezones (top-level), pandas.tests.frame.methods.test_sample (top-level), pandas.tests.arithmetic.conftest (top-level), pandas.tests.series.methods.test_reset_index (top-level), pandas.tests.extension.test_floating (top-level), pandas.tests.frame.methods.test_drop_duplicates (top-level), pandas.tests.util.test_safe_import (top-level), pandas.tests.indexes.period.methods.test_astype (top-level), pandas.tests.indexes.interval.test_interval_tree (top-level), pandas.tests.indexes.period.methods.test_to_timestamp (top-level), pandas.tests.tseries.offsets.conftest (top-level), pandas.tests.series.methods.test_quantile (top-level), pandas.tests.indexes.categorical.test_map (top-level), pandas.tests.reductions.test_stat_reductions (top-level), pandas.tests.generic.test_to_xarray (top-level), pandas.tests.internals.test_internals (top-level), pandas.tests.indexing.multiindex.test_getitem (top-level), pandas.tests.series.methods.test_sort_values (top-level), pandas.tests.arrays.period.test_arrow_compat (top-level), pandas.tests.indexes.multi.test_formats (top-level), pandas.tests.indexes.datetimes.test_unique (top-level), pandas.tests.indexing.test_loc (top-level), pandas.tests.indexes.categorical.test_equals (top-level), pandas.tests.util.test_deprecate_kwarg (top-level), pandas.tests.frame.methods.test_pipe (top-level), pandas.tests.indexes.categorical.test_reindex (top-level), pandas.tests.frame.methods.test_rename (top-level), pandas.tests.indexes.multi.test_equivalence (top-level), pandas.tests.frame.methods.test_copy (top-level), pandas.tests.indexes.multi.test_conversion (top-level), pandas.tests.indexing.test_na_indexing (top-level), pandas.tests.arrays.sparse.test_array (top-level), pandas.tests.plotting.test_misc (top-level), pandas.tests.series.methods.test_repeat (top-level), pandas.tests.util.test_assert_interval_array_equal (top-level), pandas.tests.frame.methods.test_asof (top-level), pandas.tests.plotting.frame.test_frame_legend (top-level), pandas.tests.indexes.timedeltas.methods.test_astype (top-level), pandas.tests.tslibs.test_liboffsets (top-level), pandas.tests.arrays.interval.test_ops (top-level), pandas.tests.frame.constructors.test_from_records (top-level), pandas.tests.indexes.period.test_searchsorted (top-level), pandas.tests.indexes.datetimelike_.test_nat (top-level), pandas.tests.reshape.merge.test_merge_cross (top-level), pandas.tests.scalar.interval.test_interval (top-level), pandas.tests.test_flags (top-level), pandas.tests.indexes.base_class.test_formats (top-level), pandas.tests.reshape.merge.test_merge_index_as_string (top-level), pandas.tests.dtypes.test_generic (top-level), pandas.tests.frame.methods.test_shift (top-level), pandas.tests.indexes.datetimes.test_pickle (top-level), pandas.tests.indexing.multiindex.test_iloc (top-level), pandas.tests.frame.methods.test_filter (top-level), pandas.tests.frame.indexing.test_indexing (top-level), pandas.tests.frame.methods.test_update (top-level), pandas.tests.indexes.interval.test_interval (top-level), pandas.tests.reshape.concat.test_invalid (top-level), pandas.tests.frame.methods.test_reindex (top-level), pandas.tests.series.methods.test_interpolate (top-level), pandas.tests.reshape.test_pivot (top-level), pandas.tests.indexes.multi.test_names (top-level), pandas.tests.reshape.test_crosstab (top-level), pandas.tests.util.test_validate_args (top-level), pandas.tests.arrays.categorical.test_constructors (top-level), pandas.tests.frame.methods.test_clip (top-level), pandas.tests.groupby.aggregate.test_other (top-level), pandas.tests.resample.test_period_index (top-level), pandas.tests.indexing.test_scalar (top-level), pandas.tests.arrays.test_array (top-level), pandas.tests.reshape.concat.test_dataframe (top-level), pandas.tests.series.methods.test_is_unique (top-level), pandas.tests.frame.methods.test_cov_corr (top-level), pandas.tests.extension.test_sparse (top-level), pandas.tests.libs.test_hashtable (top-level), pandas.tests.tools.test_to_timedelta (top-level), pandas.tests.indexes.multi.test_join (top-level), pandas.tests.arrays.categorical.test_warnings (top-level), pandas.tests.test_multilevel (top-level), pandas.tests.arrays.sparse.test_arithmetics (top-level), pandas.tests.series.methods.test_convert_dtypes (top-level), pandas.tests.frame.indexing.test_get_value (top-level), pandas.tests.scalar.timestamp.test_constructors (top-level), pandas.tests.indexing.test_indexers (top-level), pandas.tests.scalar.timedelta.test_formats (top-level), pandas.tests.frame.methods.test_round (top-level), pandas.tests.dtypes.cast.test_infer_dtype (top-level), pandas.tests.frame.constructors.test_from_dict (top-level), pandas.tests.indexes.multi.test_setops (top-level), pandas.tests.frame.indexing.test_set_value (top-level), pandas.tests.series.methods.test_equals (top-level), pandas.tests.series.test_logical_ops (top-level), pandas.tests.plotting.test_style (top-level), pandas.tests.tseries.frequencies.test_frequencies (top-level), pandas.tests.tseries.offsets.test_offsets_properties (top-level), pandas.tests.tseries.offsets.test_business_day (top-level), pandas.tests.indexes.datetimes.test_constructors (top-level), pandas.tests.indexes.period.test_period (top-level), pandas.tests.indexes.datetimelike (top-level), pandas.tests.plotting.frame.test_frame_groupby (top-level), pandas.tests.frame.test_query_eval (top-level), pandas.tests.plotting.test_datetimelike (top-level), pandas.tests.arrays.test_period (top-level), pandas.tests.dtypes.test_common (top-level), pandas.tests.frame.methods.test_to_period (top-level), pandas.tests.tseries.offsets.test_ticks (top-level), pandas.tests.indexes.period.methods.test_insert (top-level), pandas.tests.arrays.categorical.test_dtypes (top-level), pandas.tests.frame.methods.test_append (top-level), pandas.tests.generic.test_series (top-level), pandas.tests.arithmetic.test_numeric (top-level), pandas.tests.indexes.interval.test_interval_range (top-level), pandas.tests.tslibs.test_period_asfreq (top-level), pandas.tests.dtypes.test_dtypes (top-level), pandas.tests.series.test_cumulative (top-level), pandas.tests.scalar.interval.test_arithmetic (top-level), pandas.tests.arrays.integer.test_comparison (top-level), pandas.tests.frame.test_unary (top-level), pandas.tests.groupby.test_apply (top-level), pandas.tests.reshape.merge.test_merge (top-level), pandas.tests.groupby.test_groupby (top-level), pandas.tests.tseries.offsets.test_opening_times (top-level), pandas.tests.arrays.floating.test_comparison (top-level), pandas.tests.frame.indexing.test_xs (top-level), pandas.tests.api.test_api (top-level), pandas.tests.arrays.masked.test_function (top-level), pandas.tests.arrays.boolean.test_logical (top-level), pandas.tests.resample.test_datetime_index (top-level), pandas.tests.frame.methods.test_diff (top-level), pandas.tests.arrays.floating.conftest (top-level), pandas.tests.reshape.concat.test_append_common (top-level), pandas.tests.reshape.concat.test_index (top-level), pandas.tests.frame.methods.test_assign (top-level), pandas.tests.frame.methods.test_combine (top-level), pandas.tests.frame.methods.test_select_dtypes (top-level), pandas.tests.indexing.test_iloc (top-level), pandas.tests.arrays.categorical.test_indexing (top-level), pandas.tests.util.test_show_versions (top-level), pandas.tests.plotting.test_hist_method (top-level), pandas.tests.series.indexing.test_setitem (top-level), pandas.tests.indexes.timedeltas.methods.test_insert (top-level), pandas.tests.extension.arrow.test_timestamp (top-level), pandas.tests.strings.test_case_justify (top-level), pandas.tests.util.test_assert_attr_equal (top-level), pandas.tests.frame.methods.test_describe (top-level), pandas.tests.extension.conftest (top-level), pandas.tests.tseries.offsets.test_dst (top-level), pandas.tests.indexes.categorical.test_indexing (top-level), pandas.tests.arrays.boolean.test_function (top-level), pandas.tests.frame.indexing.test_where (top-level), pandas.tests.indexes.multi.test_isin (top-level), pandas.tests.frame.methods.test_set_axis (top-level), pandas.tests.frame.methods.test_tz_localize (top-level), pandas.tests.series.methods.test_to_dict (top-level), pandas.tests.series.methods.test_value_counts (top-level), pandas.tests.groupby.test_bin_groupby (top-level), pandas.tests.series.methods.test_replace (top-level), pandas.tests.indexes.interval.test_formats (top-level), pandas.tests.series.test_arithmetic (top-level), pandas.tests.resample.test_timedelta (top-level), pandas.tests.arrays.datetimes.test_constructors (top-level), pandas.tests.extension.test_categorical (top-level), pandas.tests.frame.methods.test_rename_axis (top-level), pandas.tests.test_aggregation (top-level), pandas.tests.indexes.base_class.test_setops (top-level), pandas.tests.reshape.concat.conftest (top-level), pandas.tests.reshape.concat.test_empty (top-level), pandas.tests.frame.methods.test_is_homogeneous_dtype (top-level), pandas.tests.groupby.test_groupby_subclass (top-level), pandas.tests.arrays.test_timedeltas (top-level), pandas.tests.indexes.datetimes.methods.test_snap (top-level), pandas.tests.indexes.test_engines (top-level), pandas.tests.dtypes.cast.test_maybe_box_native (top-level), pandas.tests.indexes.datetimes.test_scalar_compat (top-level), pandas.tests.series.methods.test_diff (top-level), pandas.tests.groupby.test_nth (top-level), pandas.tests.series.accessors.test_cat_accessor (top-level), pandas.tests.arrays.period.test_reductions (top-level), pandas.tests.arrays.datetimes.test_reductions (top-level), pandas.tests.series.methods.test_argsort (top-level), pandas.tests.indexes.period.test_join (top-level), pandas.tests.indexes.timedeltas.test_scalar_compat (top-level), pandas.tests.indexes.datetimes.methods.test_astype (top-level), pandas.tests.tseries.frequencies.test_inference (top-level), pandas.tests.arrays.floating.test_to_numpy (top-level), pandas.tests.apply.conftest (top-level), pandas.tests.arrays.sparse.test_combine_concat (top-level), pandas.tests.series.methods.test_update (top-level), pandas.tests.frame.methods.test_fillna (top-level), pandas.tests.indexes.datetimes.test_map (top-level), pandas.tests.indexes.period.methods.test_is_full (top-level), pandas.tests.indexes.multi.test_astype (top-level), pandas.tests.tseries.holiday.test_holiday (top-level), pandas.tests.indexes.categorical.test_append (top-level), pandas.tests.computation.test_compat (top-level), pandas.tests.series.methods.test_clip (top-level), pandas.tests.arithmetic.test_interval (top-level), pandas.tests.series.test_unary (top-level), pandas.tests.indexing.multiindex.test_slice (top-level), pandas.tests.test_take (top-level), pandas.tests.indexes.numeric.test_indexing (top-level), pandas.tests.series.methods.test_cov_corr (top-level), pandas.tests.frame.methods.test_to_csv (top-level), pandas.tests.reshape.merge.test_join (top-level), pandas.tests.extension.test_string (top-level), pandas.tests.extension.test_boolean (top-level), pandas.conftest (top-level), pandas.tests.dtypes.cast.test_find_common_type (top-level), pandas.tests.frame.indexing.test_get (top-level), pandas.tests.util.test_assert_series_equal (top-level), pandas.tests.frame.methods.test_droplevel (top-level), pandas.tests.indexes.datetimes.test_partial_slicing (top-level), pandas.tests.series.indexing.test_take (top-level), pandas.tests.arrays.integer.test_repr (top-level), pandas.tests.arrays.test_datetimelike (top-level), pandas.tests.groupby.test_grouping (top-level), pandas.tests.indexes.datetimes.test_date_range (top-level), pandas.tests.arrays.categorical.test_analytics (top-level), pandas.tests.arrays.period.test_astype (top-level), pandas.tests.series.accessors.test_str_accessor (top-level), pandas.tests.util.test_assert_categorical_equal (top-level), pandas.tests.frame.methods.test_transpose (top-level), pandas.tests.groupby.test_missing (top-level), pandas.tests.indexes.multi.test_monotonic (top-level), pandas.tests.frame.methods.test_join (top-level), pandas.tests.groupby.test_quantile (top-level), pandas.tests.arrays.integer.test_function (top-level), pandas.tests.indexes.period.test_period_range (top-level), pandas.tests.frame.methods.test_between_time (top-level), pandas.tests.reshape.concat.test_datetimes (top-level), pandas.tests.series.indexing.test_indexing (top-level), pandas.tests.indexes.period.test_partial_slicing (top-level), pandas.tests.frame.test_constructors (top-level), pandas.tests.strings.test_extract (top-level), pandas.tests.apply.test_series_apply (top-level), pandas.tests.indexing.multiindex.test_chaining_and_caching (top-level), pandas.tests.arrays.boolean.test_reduction (top-level), pandas.tests.plotting.test_common (top-level), pandas.tests.series.methods.test_drop (top-level), pandas.tests.frame.methods.test_set_index (top-level), pandas.tests.scalar.period.test_asfreq (top-level), pandas.tests.scalar.timestamp.test_timezones (top-level), pandas.tests.extension.test_extension (top-level), pandas.tests.frame.test_logical_ops (top-level), pandas.tests.indexes.base_class.test_indexing (top-level), pandas.tests.frame.methods.test_swaplevel (top-level), pandas.tests.indexes.interval.test_astype (top-level), pandas.tests.frame.conftest (top-level), pandas.tests.indexes.test_setops (top-level), pandas.tests.indexing.multiindex.test_sorted (top-level), pandas.tests.frame.test_reductions (top-level), pandas.tests.groupby.test_timegrouper (top-level), pandas.tests.plotting.test_groupby (top-level), pandas.tests.reshape.test_qcut (top-level), pandas.tests.series.methods.test_asfreq (top-level), pandas.tests.indexes.multi.test_analytics (top-level), pandas.tests.util.test_hashing (top-level), pandas.tests.indexes.multi.test_missing (top-level), pandas.tests.indexes.object.test_indexing (top-level), pandas.tests.indexes.numeric.test_setops (top-level), pandas.tests.resample.test_deprecated (top-level), pandas.tests.scalar.interval.test_ops (top-level), pandas.tests.arrays.integer.test_arithmetic (top-level), pandas.tests.indexes.multi.test_indexing (top-level), pandas.tests.tslibs.test_to_offset (top-level), pandas.tests.groupby.test_min_max (top-level), pandas.tests.indexing.test_floats (top-level), pandas.tests.groupby.test_groupby_dropna (top-level), pandas.tests.util.test_assert_numpy_array_equal (top-level), pandas.tests.groupby.test_value_counts (top-level), pandas.tests.base.test_unique (top-level), pandas.tests.series.methods.test_unstack (top-level), pandas.tests.test_downstream (top-level), pandas.tests.indexes.numeric.test_join (top-level), pandas.tests.scalar.timestamp.test_arithmetic (top-level), pandas.tests.reshape.test_util (top-level), pandas.tests.series.methods.test_shift (top-level), pandas.tests.test_register_accessor (top-level), pandas.tests.series.methods.test_explode (top-level), pandas.tests.tslibs.test_conversion (top-level), pandas.tests.extension.test_common (top-level), pandas.tests.arrays.interval.test_interval (top-level), pandas.tests.resample.test_time_grouper (top-level), pandas.tests.test_nanops (top-level), pandas.tests.indexes.datetimes.test_join (top-level), pandas.tests.arrays.timedeltas.test_constructors (top-level), pandas.tests.base.test_constructors (top-level), pandas.tests.series.methods.test_nlargest (top-level), pandas.tests.scalar.timedelta.test_arithmetic (top-level), pandas.tests.frame.methods.test_replace (top-level), pandas.tests.arrays.categorical.test_algos (top-level), pandas.tests.arrays.integer.test_concat (top-level), pandas.tests.arrays.sparse.test_dtype (top-level), pandas.tests.extension.list.test_list (top-level), pandas.tests.series.methods.test_convert (top-level), pandas.tests.groupby.test_libgroupby (top-level), pandas.tests.reshape.test_melt (top-level), pandas.tests.indexes.multi.conftest (top-level), pandas.tests.indexes.datetimes.methods.test_insert (top-level), pandas.tests.indexes.interval.test_base (top-level), pandas.tests.frame.test_stack_unstack (top-level), pandas.tests.indexes.datetimes.methods.test_fillna (top-level), pandas.tests.series.methods.test_values (top-level), pandas.tests.arrays.categorical.test_take (top-level), pandas.tests.frame.indexing.test_setitem (top-level), pandas.tests.util.test_deprecate (top-level), pandas.tests.extension.json.test_json (top-level), pandas.tests.arrays.boolean.test_construction (top-level), pandas.tests.plotting.test_converter (top-level), pandas.tests.arithmetic.test_period (top-level), pandas.tests.arithmetic.common (top-level), pandas.tests.frame.test_nonunique_indexes (top-level), pandas.tests.indexing.interval.test_interval_new (top-level), pandas.tests.arrays.categorical.test_replace (top-level), pandas.tests.series.methods.test_item (top-level), pandas.tests.series.indexing.test_datetime (top-level), pandas.tests.series.methods.test_append (top-level), pandas.tests.plotting.frame.test_frame (top-level), pandas.tests.scalar.timedelta.test_constructors (top-level), pandas.tests.series.test_validate (top-level), pandas.tests.indexes.datetimes.test_setops (top-level), pandas.tests.tslibs.test_ccalendar (top-level), pandas.tests.reshape.merge.test_merge_ordered (top-level), pandas.tests.tseries.holiday.test_observance (top-level), pandas.tests.series.indexing.test_get (top-level), pandas.tests.plotting.frame.test_frame_color (top-level), pandas.tests.tseries.offsets.test_month (top-level), pandas.tests.scalar.test_nat (top-level), pandas.tests.frame.methods.test_reset_index (top-level), pandas.tests.frame.methods.test_truncate (top-level), pandas.tests.series.methods.test_round (top-level), pandas.tests.frame.methods.test_matmul (top-level), pandas.tests.tslibs.test_parse_iso8601 (top-level), pandas.tests.arrays.categorical.test_operators (top-level), pandas.tests.arrays.masked.test_arrow_compat (top-level), pandas.tests.tslibs.test_libfrequencies (top-level), pandas.tests.scalar.timedelta.test_timedelta (top-level), pandas.tests.frame.methods.test_dropna (top-level), pandas.tests.extension.arrow.test_string (top-level), pandas.tests.reshape.test_pivot_multilevel (top-level), pandas.tests.indexing.multiindex.test_loc (top-level), pandas.tests.extension.test_datetime (top-level), pandas.tests.groupby.aggregate.test_numba (top-level), pandas.tests.indexes.datetimes.methods.test_to_series (top-level), pandas.tests.arrays.floating.test_concat (top-level), pandas.tests.groupby.test_index_as_string (top-level), pandas.tests.indexes.timedeltas.test_ops (top-level), pandas.tests.apply.test_frame_transform (top-level), pandas.tests.series.methods.test_compare (top-level), pandas.tests.indexes.multi.test_sorting (top-level), pandas.tests.indexes.period.test_ops (top-level), pandas.tests.series.methods.test_pct_change (top-level), pandas.tests.indexes.period.test_indexing (top-level), pandas.tests.apply.test_frame_apply (top-level), pandas.tests.libs.test_join (top-level), pandas.tests.indexing.interval.test_interval (top-level), pandas.tests.series.methods.test_reindex (top-level), pandas.tests.reshape.test_union_categoricals (top-level), pandas.tests.indexes.datetimelike_.test_sort_values (top-level), pandas.tests.groupby.aggregate.test_cython (top-level), pandas.tests.config.test_localization (top-level), pandas.tests.indexes.interval.test_constructors (top-level), pandas.tests.indexes.test_frozen (top-level), pandas.tests.groupby.test_filters (top-level), pandas.tests.frame.indexing.test_lookup (top-level), pandas.tests.tseries.offsets.test_yqm_offsets (top-level), pandas.tests.io (top-level), pandas.tests.frame.methods.test_drop (top-level), pandas.tests.reshape.test_get_dummies (top-level), pandas.tests.series.methods.test_rename_axis (top-level), pandas.tests.series.test_api (top-level), pandas.tests.arrays.boolean.test_arithmetic (top-level), pandas.tests.generic.test_finalize (top-level), pandas.tests.groupby.test_counting (top-level), pandas.tests.frame.methods.test_rank (top-level), pandas.tests.groupby.test_nunique (top-level), pandas.tests.series.indexing.test_mask (top-level), pandas.tests.frame.methods.test_align (top-level), pandas.tests.strings.test_split_partition (top-level), pandas.tests.config.test_config (top-level), pandas.tests.series.methods.test_sort_index (top-level), pandas.tests.series.test_constructors (top-level), pandas.tests.reshape.concat.test_append (top-level), pandas.tests.extension.test_interval (top-level), pandas.tests.indexes.period.test_tools (top-level), pandas.tests.indexes.test_common (top-level), pandas.tests.tslibs.test_timedeltas (top-level), pandas.tests.indexing.multiindex.test_partial (top-level), pandas.tests.indexes.timedeltas.test_timedelta (top-level), pandas.tests.util.test_assert_frame_equal (top-level), pandas.tests.dtypes.cast.test_construct_object_arr (top-level), pandas.tests.test_sorting (top-level), pandas.tests.base.test_fillna (top-level), pandas.tests.frame.methods.test_first_valid_index (top-level), pandas.tests.series.indexing.test_delitem (top-level), pandas.tests.groupby.test_any_all (top-level), pandas.tests.indexes.ranges.test_indexing (top-level), pandas.tests.series.indexing.test_getitem (top-level), pandas.tests.frame.methods.test_dot (top-level), pandas.tests.extension.test_numpy (top-level), pandas.tests.indexes.period.test_formats (top-level), pandas.tests.series.methods.test_count (top-level), pandas.tests.groupby.test_function (top-level), pandas.tests.frame.methods.test_at_time (top-level), pandas.tests.indexes.conftest (top-level), pandas.tests.indexing.test_categorical (top-level), pandas.tests.frame.methods.test_pct_change (top-level), pandas.tests.frame.methods.test_nlargest (top-level), pandas.tests.indexes.base_class.test_reshape (top-level), pandas.tests.test_errors (top-level), pandas.tests.util.test_validate_args_and_kwargs (top-level), pandas.tests.frame.methods.test_interpolate (top-level), pandas.tests.groupby.conftest (top-level), pandas.tests.util.test_assert_produces_warning (top-level), pandas.tests.base.test_misc (top-level), pandas.tests.indexes.multi.test_drop (top-level), pandas.tests.arrays.interval.test_astype (top-level), pandas.tests.frame.test_ufunc (top-level), pandas.tests.tools.test_to_time (top-level), pandas.tests.frame.indexing.test_take (top-level), pandas.tests.series.methods.test_asof (top-level), pandas.tests.frame.methods.test_to_records (top-level), pandas.tests.arrays.categorical.conftest (top-level), pandas.tests.tseries.offsets.test_business_hour (top-level), pandas.tests.frame.test_repr_info (top-level), pandas.tests.util.conftest (top-level), pandas.tests.series.methods.test_between (top-level), pandas.tests.arrays.floating.test_construction (top-level), pandas.tests.arithmetic.test_datetime64 (top-level), pandas.tests.frame.indexing.test_getitem (top-level), pandas.tests.arrays.sparse.test_accessor (top-level), pandas.tests.strings.test_string_array (top-level), pandas.tests.indexes.multi.test_partial_indexing (top-level), pandas.tests.extension.decimal.test_decimal (top-level), pandas.tests.frame.methods.test_swapaxes (top-level), pandas.tests.dtypes.cast.test_construct_ndarray (top-level), pandas.tests.frame.methods.test_to_timestamp (top-level), pandas.tests.test_common (top-level), pandas.tests.indexes.datetimes.methods.test_to_period (top-level), pandas.tests.strings.test_cat (top-level), pandas.tests.series.methods.test_rank (top-level), pandas.tests.indexing.test_partial (top-level), pandas.tests.util.test_assert_almost_equal (top-level), pandas.tests.indexes.datetimes.methods.test_repeat (top-level), pandas.tests.frame.methods.test_first_and_last (top-level), pandas.tests.apply.test_invalid_arg (top-level), pandas.tests.strings.test_api (top-level), pandas.tests.groupby.aggregate.test_aggregate (top-level), pandas.tests.indexes.interval.test_setops (top-level), pandas.tests.indexing.test_at (top-level), pandas.tests.strings.conftest (top-level), pandas.tests.generic.test_duplicate_labels (top-level), pandas.tests.groupby.test_categorical (top-level), pandas.tests.tseries.frequencies.test_freq_code (top-level), pandas.tests.arrays.sparse.test_libsparse (top-level), pandas.tests.util.test_assert_extension_array_equal (top-level), pandas.tests.strings.test_find_replace (top-level), pandas.tests.dtypes.cast.test_promote (top-level), pandas.tests.indexes.timedeltas.test_timedelta_range (top-level), pandas.tests.groupby.test_allowlist (top-level), pandas.tests.indexes.categorical.test_category (top-level), pandas.tests.plotting.test_backend (top-level), pandas.tests.frame.test_validate (top-level), pandas.tests.frame.methods.test_compare (top-level), pandas.tests.extension.arrow.test_bool (top-level), pandas.tests.frame.test_arithmetic (top-level), pandas.tests.scalar.timestamp.test_timestamp (top-level), pandas.tests.arrays.test_numpy (top-level), pandas.tests.indexes.test_base (top-level), pandas.tests.indexes.test_index_new (top-level), pandas.tests.arrays.masked.test_arithmetic (top-level), pandas.tests.dtypes.cast.test_infer_datetimelike (top-level), pandas.tests.indexes.ranges.test_range (top-level), pandas.tests.reshape.concat.test_concat (top-level), pandas.tests.series.methods.test_align (top-level), pandas.tests.base.test_transpose (top-level), pandas.tests.series.methods.test_duplicated (top-level), pandas.tests.window (top-level), pandas.tests.apply.test_series_transform (top-level), pandas.tests.indexes.multi.test_duplicates (top-level), pandas.tests.frame.test_block_internals (top-level), pandas.tests.test_algos (top-level), pandas.tests.arrays.timedeltas.test_reductions (top-level), pandas.tests.indexes.datetimelike_.test_indexing (top-level), pandas.tests.indexes.categorical.test_fillna (top-level), pandas.tests.indexes.test_any_index (top-level), pandas.tests.scalar.timestamp.test_unary_ops (top-level), pandas.tests.base.test_value_counts (top-level), pandas.tests.extension.test_integer (top-level), pandas.tests.series.test_reductions (top-level), pandas.tests.dtypes.test_concat (top-level), pandas.tests.indexes.timedeltas.methods.test_shift (top-level), pandas.tests.resample.test_base (top-level), pandas.tests.computation.test_eval (top-level), pandas.tests.arrays.integer.test_construction (top-level), pandas.tests.resample.conftest (top-level), pandas.tests.indexes.datetimes.test_formats (top-level), pandas.tests.indexes.datetimes.methods.test_shift (top-level), pandas.tests.scalar.period.test_period (top-level), pandas.tests.reductions.test_reductions (top-level), pandas.tests.arrays.boolean.test_astype (top-level), pandas.tests.frame.methods.test_convert_dtypes (top-level), pandas.tests.frame.test_api (top-level), pandas.tests.indexes.datetimes.test_datetimelike (top-level), pandas.tests.frame.methods.test_astype (top-level), pandas.tests.indexing.test_indexing (top-level), pandas.tests.libs.test_lib (top-level), pandas.tests.scalar.test_na_scalar (top-level), pandas.tests.series.methods.test_isin (top-level), pandas.tests.groupby.transform.test_transform (top-level), pandas.tests.tseries.offsets.test_week (top-level), pandas.tests.arrays.integer.test_dtypes (top-level), pandas.tests.indexes.multi.test_get_set (top-level), pandas.tests.indexes.base_class.test_constructors (top-level), pandas.tests.indexing.multiindex.test_setitem (top-level), pandas.tests.arrays.categorical.test_sorting (top-level), pandas.tests.indexes.timedeltas.test_searchsorted (top-level), pandas.tests.frame.test_subclass (top-level), pandas.tests.indexes.multi.test_copy (top-level), pandas.tests.series.accessors.test_dt_accessor (top-level), pandas.tests.arrays.integer.conftest (top-level), pandas.tests.indexes.multi.test_take (top-level), pandas.tests.indexes.multi.test_reshape (top-level), pandas.tests.base.test_conversion (top-level), pandas.tests.arrays.floating.test_repr (top-level), pandas.tests.tseries.offsets.test_custom_business_hour (top-level), pandas.tests.tseries.offsets.test_fiscal (top-level), pandas.tests.test_optional_dependency (top-level), pandas.tests.indexes.timedeltas.test_indexing (top-level), pandas.tests.reshape.test_cut (top-level), pandas.tests.tools.test_to_numeric (top-level), pandas.tests.indexes.multi.test_integrity (top-level), pandas.tests.indexing.test_coercion (top-level), pandas.tests.indexes.timedeltas.test_constructors (top-level), pandas.tests.indexes.period.methods.test_shift (top-level), pandas.tests.plotting.test_series (top-level), pandas.tests.util.test_util (top-level), pandas.tests.indexing.test_chaining_and_caching (top-level), pandas.tests.arithmetic.test_timedelta64 (top-level), pandas.tests.frame.methods.test_count_with_level_deprecated (top-level), pandas.tests.indexes.datetimes.test_misc (top-level), pandas.tests.arrays.floating.test_arithmetic (top-level), pandas.tests.indexes.datetimes.test_delete (top-level), pandas.tests.arrays.period.test_constructors (top-level), pandas.tests.arrays.floating.test_astype (top-level), pandas.tests.series.test_ufunc (top-level)
missing module named pyarrow - imported by pandas.core.arrays.masked (delayed), pandas.core.arrays.numeric (delayed, conditional), pandas.core.arrays._arrow_utils (top-level), pandas.core.arrays.interval (delayed), pandas.core.arrays.period (delayed), pandas.core.arrays.boolean (delayed, conditional), pandas.io.feather_format (delayed), pandas.core.arrays.string_ (delayed, conditional), pandas.core.arrays.string_arrow (conditional), pandas.core.dtypes.dtypes (delayed, conditional), pandas.compat.pyarrow (optional), pandas.tests.arrays.string_.test_string (delayed, conditional), pandas.tests.arrays.string_.test_string_arrow (delayed), pandas.tests.arrays.period.test_arrow_compat (delayed), pandas.tests.extension.arrow.test_timestamp (top-level), pandas.tests.extension.arrow.arrays (top-level), pandas.tests.extension.test_string (delayed), pandas.tests.arrays.interval.test_interval (delayed)
missing module named 'pyarrow.parquet' - imported by pandas.io.parquet (delayed)
missing module named 'matplotlib.colors' - imported by pandas.plotting._matplotlib.style (top-level), pandas.plotting._matplotlib.core (delayed), pandas.tests.plotting.common (delayed), pandas.tests.plotting.test_style (delayed), pandas.tests.plotting.test_series (delayed)
missing module named 'matplotlib.pyplot' - imported by pandas.io.formats.style (optional), pandas._testing._io (delayed), pandas._testing.asserters (delayed), pandas.util._doctools (delayed, conditional), pandas.plotting._matplotlib.style (delayed), pandas.plotting._matplotlib.tools (delayed), pandas.plotting._matplotlib.core (delayed), pandas.plotting._matplotlib.boxplot (delayed), pandas.plotting._matplotlib.hist (delayed), pandas.plotting._matplotlib.misc (delayed), pandas.plotting._matplotlib (delayed), pandas.tests.plotting.common (delayed), pandas.tests.plotting.frame.test_frame_subplots (delayed), pandas.tests.plotting.test_boxplot_method (delayed), pandas.tests.plotting.test_style (delayed), pandas.tests.plotting.test_datetimelike (delayed), pandas.tests.plotting.test_hist_method (delayed), pandas.tests.plotting.frame.test_frame (delayed), pandas.tests.plotting.frame.test_frame_color (delayed)
missing module named 'matplotlib.cm' - imported by pandas.plotting._matplotlib.style (top-level)
missing module named 'matplotlib.units' - imported by pandas.plotting._matplotlib.converter (top-level)
missing module named 'matplotlib.transforms' - imported by pandas.plotting._matplotlib.converter (top-level)
missing module named 'matplotlib.ticker' - imported by pandas.plotting._matplotlib.converter (top-level), pandas.plotting._matplotlib.tools (top-level), pandas.plotting._matplotlib.core (delayed), pandas.tests.plotting.common (delayed)
missing module named 'matplotlib.dates' - imported by pandas.plotting._matplotlib.converter (top-level)
missing module named six.moves.range - imported by six.moves (top-level), dateutil.rrule (top-level)
runtime module named six.moves - imported by dateutil.tz.tz (top-level), dateutil.tz._factories (top-level), dateutil.tz.win (top-level), dateutil.rrule (top-level)
missing module named dateutil.tz.tzfile - imported by dateutil.tz (top-level), dateutil.zoneinfo (top-level)
excluded module named matplotlib - imported by pandas.io.formats.style (optional), pandas.tests.plotting.common (delayed), pandas.plotting._matplotlib.compat (delayed, optional), pandas.plotting._matplotlib.timeseries (delayed), pandas.plotting._matplotlib.core (delayed, conditional), pandas.tests.plotting.frame.test_frame_subplots (delayed), pandas.tests.plotting.test_boxplot_method (delayed), pandas.tests.plotting.test_misc (delayed), pandas.tests.plotting.test_style (delayed), pandas.tests.plotting.frame.test_frame_groupby (delayed), pandas.tests.plotting.test_hist_method (delayed), pandas.tests.plotting.frame.test_frame (delayed), pandas.tests.plotting.frame.test_frame_color (delayed), pandas.tests.plotting.test_series (delayed)
missing module named numexpr - imported by pandas.core.computation.expressions (conditional), pandas.core.computation.engines (delayed), pandas.tests.computation.test_compat (delayed, conditional, optional), pandas.tests.computation.test_eval (delayed)
missing module named 'IPython.core' - imported by pandas.io.formats.printing (delayed, conditional), pandas.tests.resample.test_resampler_grouper (delayed), pandas.tests.arrays.categorical.test_warnings (delayed), pandas.conftest (delayed), pandas.tests.indexes.test_base (delayed), pandas.tests.frame.test_api (delayed)
missing module named IPython - imported by pandas.io.formats.printing (delayed)
missing module named 'matplotlib.lines' - imported by pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.misc (top-level), pandas.tests.plotting.common (delayed), pandas.tests.plotting.frame.test_frame_legend (delayed)
missing module named 'matplotlib.figure' - imported by pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.misc (conditional)
missing module named 'matplotlib.axis' - imported by pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.core (conditional)
missing module named 'matplotlib.axes' - imported by pandas.tests.plotting.common (delayed, conditional), pandas.plotting._matplotlib.timeseries (conditional), pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.core (delayed, conditional), pandas.plotting._matplotlib.boxplot (conditional), pandas.plotting._matplotlib.hist (conditional), pandas.plotting._matplotlib.misc (conditional)
missing module named 'matplotlib.table' - imported by pandas.plotting._matplotlib.tools (top-level)
missing module named 'scipy.stats' - imported by pandas.core.nanops (delayed, conditional), pandas.plotting._matplotlib.hist (delayed), pandas.plotting._matplotlib.misc (delayed, conditional), pandas.tests.reductions.test_stat_reductions (delayed), pandas.tests.series.methods.test_cov_corr (delayed), pandas.tests.frame.test_reductions (delayed, optional), pandas.tests.test_nanops (delayed), pandas.tests.frame.methods.test_rank (delayed), pandas.tests.groupby.test_function (delayed), pandas.tests.test_algos (delayed)
missing module named 'matplotlib.patches' - imported by pandas.plotting._matplotlib.misc (top-level), pandas.tests.plotting.test_hist_method (delayed), pandas.tests.plotting.frame.test_frame (delayed)
missing module named 'matplotlib.artist' - imported by pandas.plotting._matplotlib.boxplot (top-level), pandas.plotting._matplotlib.core (top-level)
missing module named 'scipy.sparse' - imported by pandas.core.arrays.sparse.scipy_sparse (delayed), pandas.core.arrays.sparse.accessor (delayed), pandas.core.dtypes.common (delayed, conditional, optional), pandas.tests.indexing.test_loc (delayed), pandas.tests.arrays.sparse.test_array (delayed), pandas.tests.dtypes.test_common (delayed, conditional), pandas.tests.arrays.sparse.test_accessor (delayed)
missing module named numba - imported by pandas.tests.groupby.transform.test_numba (delayed, conditional), pandas.tests.groupby.aggregate.test_numba (delayed, conditional), pandas.tests.frame.test_ufunc (delayed)
missing module named pyxlsb - imported by pandas.io.excel._pyxlsb (delayed)
missing module named 'pyarrow.compute' - imported by pandas.core.arrays.string_arrow (conditional)
missing module named cycler - imported by pandas.tests.plotting.frame.test_frame_color (delayed)
missing module named 'matplotlib.collections' - imported by pandas.tests.plotting.common (delayed), pandas.tests.plotting.frame.test_frame_legend (delayed), pandas.tests.plotting.frame.test_frame_color (delayed)
missing module named hypothesis - imported by pandas.util._tester (delayed, optional), pandas.tests.tseries.offsets.test_offsets_properties (top-level), pandas.tests.tseries.offsets.test_ticks (top-level), pandas.conftest (top-level), pandas.tests.tslibs.test_ccalendar (top-level)
missing module named 'mpl_toolkits.axes_grid1' - imported by pandas.tests.plotting.frame.test_frame (delayed)
missing module named mpl_toolkits - imported by pandas.tests.plotting.frame.test_frame (delayed)
missing module named 'matplotlib.gridspec' - imported by pandas.util._doctools (delayed), pandas.tests.plotting.frame.test_frame (delayed)
missing module named 'odf.text' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.element' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.namespaces' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.table' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.opendocument' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named odf - imported by pandas.io.excel._odfreader (delayed)
missing module named sklearn - imported by pandas.tests.test_downstream (delayed)
missing module named 'statsmodels.formula' - imported by pandas.tests.test_downstream (delayed)
missing module named statsmodels - imported by pandas.tests.test_downstream (delayed)
missing module named xarray - imported by pandas.tests.generic.test_to_xarray (delayed), pandas.tests.arrays.test_datetimelike (delayed, conditional), pandas.tests.test_downstream (delayed)
missing module named cftime - imported by pandas.tests.test_downstream (delayed)
missing module named 'dask.dataframe' - imported by pandas.tests.test_downstream (delayed)
missing module named 'lxml.html' - imported by pandas.io.html (delayed)
missing module named bs4 - imported by pandas.io.html (delayed)
missing module named scipy - imported by pandas.core.missing (delayed), pandas.conftest (delayed), pandas.tests.frame.test_reductions (delayed, optional)
missing module named xlsxwriter - imported by pandas.io.excel._xlsxwriter (delayed)
missing module named dask - imported by pandas.tests.arrays.test_datetimelike (delayed, conditional)
missing module named 'fsspec.registry' - imported by pandas.conftest (delayed)
missing module named 'fsspec.implementations' - imported by pandas.conftest (delayed)
missing module named fsspec - imported by pandas.conftest (delayed)
missing module named traitlets - imported by pandas.conftest (delayed)
missing module named pylab - imported by pandas.tests.plotting.test_hist_method (delayed)
missing module named 'hypothesis.extra' - imported by pandas.tests.tseries.offsets.test_offsets_properties (top-level)
missing module named 'hypothesis.errors' - imported by pandas.tests.tseries.offsets.test_offsets_properties (top-level)
missing module named pandas.ExtensionArray - imported by pandas (conditional), pandas.core.construction (conditional)
missing module named botocore - imported by pandas.io.common (delayed, conditional, optional)
missing module named xlrd - imported by pandas.io.excel._xlrd (delayed), pandas.io.excel._base (delayed, conditional)
missing module named 'sqlalchemy.types' - imported by pandas.io.sql (delayed, conditional)
missing module named 'sqlalchemy.schema' - imported by pandas.io.sql (delayed, conditional)
missing module named sqlalchemy - imported by pandas.io.sql (delayed, conditional, optional)
missing module named markupsafe - imported by pandas.io.formats.style_render (top-level)
missing module named _uuid - imported by uuid (optional)
missing module named netbios - imported by uuid (delayed)
missing module named win32wnet - imported by uuid (delayed)
missing module named tables - imported by pandas.io.pytables (delayed, conditional)
missing module named xlwt - imported by pandas.io.excel._xlwt (delayed, conditional)
missing module named 'odf.config' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.style' - imported by pandas.io.excel._odswriter (delayed)
missing module named Foundation - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named AppKit - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named PyQt4 - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named PyQt5 - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named qtpy - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named psycopg2 - imported by pandas.tests.tools.test_to_datetime (delayed)
missing module named vms_lib - imported by platform (delayed, conditional, optional)
missing module named java - imported by platform (delayed)
missing module named 'multiprocessing.queues' - imported by concurrent.futures.process (top-level)
missing module named 'multiprocessing.connection' - imported by concurrent.futures.process (top-level)
excluded module named multiprocessing - imported by lib2to3.refactor (delayed, optional), numpy.distutils.misc_util (top-level), concurrent.futures.process (top-level)
missing module named posix - imported by os (conditional, optional)
missing module named resource - imported by posix (top-level)
