('E:\\code\\yiyaoxinxichuli\\build\\Medical_System\\PYZ-00.pyz',
 [('pkg_resources',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('packaging',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.utils',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.metadata',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('typing_extensions',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\anaconda3\\envs\\dabao\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email', 'D:\\anaconda3\\envs\\dabao\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email._header_value_parser',
   'D:\\anaconda3\\envs\\dabao\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._encoded_words',
   'D:\\anaconda3\\envs\\dabao\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'D:\\anaconda3\\envs\\dabao\\lib\\base64.py', 'PYMODULE'),
  ('getopt', 'D:\\anaconda3\\envs\\dabao\\lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'D:\\anaconda3\\envs\\dabao\\lib\\gettext.py', 'PYMODULE'),
  ('copy', 'D:\\anaconda3\\envs\\dabao\\lib\\copy.py', 'PYMODULE'),
  ('struct', 'D:\\anaconda3\\envs\\dabao\\lib\\struct.py', 'PYMODULE'),
  ('string', 'D:\\anaconda3\\envs\\dabao\\lib\\string.py', 'PYMODULE'),
  ('urllib',
   'D:\\anaconda3\\envs\\dabao\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\anaconda3\\envs\\dabao\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\anaconda3\\envs\\dabao\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('quopri', 'D:\\anaconda3\\envs\\dabao\\lib\\quopri.py', 'PYMODULE'),
  ('email.quoprimime',
   'D:\\anaconda3\\envs\\dabao\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\anaconda3\\envs\\dabao\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\anaconda3\\envs\\dabao\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\anaconda3\\envs\\dabao\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\anaconda3\\envs\\dabao\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\anaconda3\\envs\\dabao\\lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\anaconda3\\envs\\dabao\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar', 'D:\\anaconda3\\envs\\dabao\\lib\\calendar.py', 'PYMODULE'),
  ('argparse', 'D:\\anaconda3\\envs\\dabao\\lib\\argparse.py', 'PYMODULE'),
  ('urllib.parse',
   'D:\\anaconda3\\envs\\dabao\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('socket', 'D:\\anaconda3\\envs\\dabao\\lib\\socket.py', 'PYMODULE'),
  ('selectors', 'D:\\anaconda3\\envs\\dabao\\lib\\selectors.py', 'PYMODULE'),
  ('random', 'D:\\anaconda3\\envs\\dabao\\lib\\random.py', 'PYMODULE'),
  ('bisect', 'D:\\anaconda3\\envs\\dabao\\lib\\bisect.py', 'PYMODULE'),
  ('hashlib', 'D:\\anaconda3\\envs\\dabao\\lib\\hashlib.py', 'PYMODULE'),
  ('logging',
   'D:\\anaconda3\\envs\\dabao\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle', 'D:\\anaconda3\\envs\\dabao\\lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\anaconda3\\envs\\dabao\\lib\\pprint.py', 'PYMODULE'),
  ('doctest', 'D:\\anaconda3\\envs\\dabao\\lib\\doctest.py', 'PYMODULE'),
  ('pdb', 'D:\\anaconda3\\envs\\dabao\\lib\\pdb.py', 'PYMODULE'),
  ('pydoc', 'D:\\anaconda3\\envs\\dabao\\lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser', 'D:\\anaconda3\\envs\\dabao\\lib\\webbrowser.py', 'PYMODULE'),
  ('shutil', 'D:\\anaconda3\\envs\\dabao\\lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'D:\\anaconda3\\envs\\dabao\\lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'D:\\anaconda3\\envs\\dabao\\lib\\gzip.py', 'PYMODULE'),
  ('_compression',
   'D:\\anaconda3\\envs\\dabao\\lib\\_compression.py',
   'PYMODULE'),
  ('lzma', 'D:\\anaconda3\\envs\\dabao\\lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'D:\\anaconda3\\envs\\dabao\\lib\\bz2.py', 'PYMODULE'),
  ('fnmatch', 'D:\\anaconda3\\envs\\dabao\\lib\\fnmatch.py', 'PYMODULE'),
  ('http.server',
   'D:\\anaconda3\\envs\\dabao\\lib\\http\\server.py',
   'PYMODULE'),
  ('http', 'D:\\anaconda3\\envs\\dabao\\lib\\http\\__init__.py', 'PYMODULE'),
  ('socketserver',
   'D:\\anaconda3\\envs\\dabao\\lib\\socketserver.py',
   'PYMODULE'),
  ('mimetypes', 'D:\\anaconda3\\envs\\dabao\\lib\\mimetypes.py', 'PYMODULE'),
  ('http.client',
   'D:\\anaconda3\\envs\\dabao\\lib\\http\\client.py',
   'PYMODULE'),
  ('ssl', 'D:\\anaconda3\\envs\\dabao\\lib\\ssl.py', 'PYMODULE'),
  ('html', 'D:\\anaconda3\\envs\\dabao\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'D:\\anaconda3\\envs\\dabao\\lib\\html\\entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\anaconda3\\envs\\dabao\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'D:\\anaconda3\\envs\\dabao\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty', 'D:\\anaconda3\\envs\\dabao\\lib\\tty.py', 'PYMODULE'),
  ('subprocess', 'D:\\anaconda3\\envs\\dabao\\lib\\subprocess.py', 'PYMODULE'),
  ('importlib.util',
   'D:\\anaconda3\\envs\\dabao\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('contextlib', 'D:\\anaconda3\\envs\\dabao\\lib\\contextlib.py', 'PYMODULE'),
  ('importlib.abc',
   'D:\\anaconda3\\envs\\dabao\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\anaconda3\\envs\\dabao\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\anaconda3\\envs\\dabao\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('runpy', 'D:\\anaconda3\\envs\\dabao\\lib\\runpy.py', 'PYMODULE'),
  ('shlex', 'D:\\anaconda3\\envs\\dabao\\lib\\shlex.py', 'PYMODULE'),
  ('signal', 'D:\\anaconda3\\envs\\dabao\\lib\\signal.py', 'PYMODULE'),
  ('code', 'D:\\anaconda3\\envs\\dabao\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\anaconda3\\envs\\dabao\\lib\\codeop.py', 'PYMODULE'),
  ('dis', 'D:\\anaconda3\\envs\\dabao\\lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\anaconda3\\envs\\dabao\\lib\\opcode.py', 'PYMODULE'),
  ('bdb', 'D:\\anaconda3\\envs\\dabao\\lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'D:\\anaconda3\\envs\\dabao\\lib\\cmd.py', 'PYMODULE'),
  ('__future__', 'D:\\anaconda3\\envs\\dabao\\lib\\__future__.py', 'PYMODULE'),
  ('_compat_pickle',
   'D:\\anaconda3\\envs\\dabao\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\anaconda3\\envs\\dabao\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.message',
   'D:\\anaconda3\\envs\\dabao\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\anaconda3\\envs\\dabao\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\anaconda3\\envs\\dabao\\lib\\email\\generator.py',
   'PYMODULE'),
  ('uu', 'D:\\anaconda3\\envs\\dabao\\lib\\uu.py', 'PYMODULE'),
  ('optparse', 'D:\\anaconda3\\envs\\dabao\\lib\\optparse.py', 'PYMODULE'),
  ('email.header',
   'D:\\anaconda3\\envs\\dabao\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\anaconda3\\envs\\dabao\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('packaging._parser',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('ast', 'D:\\anaconda3\\envs\\dabao\\lib\\ast.py', 'PYMODULE'),
  ('packaging.markers',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('dataclasses',
   'D:\\anaconda3\\envs\\dabao\\lib\\dataclasses.py',
   'PYMODULE'),
  ('packaging._elffile',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging.tags',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.requirements',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('ctypes',
   'D:\\anaconda3\\envs\\dabao\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\anaconda3\\envs\\dabao\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\anaconda3\\envs\\dabao\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.testing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.context',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.abc',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._itertools',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._manylinux',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._adapters',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.functools',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.common',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.core',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\core.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.unicode',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.readers',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.results',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\results.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.actions',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.more',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('queue', 'D:\\anaconda3\\envs\\dabao\\lib\\queue.py', 'PYMODULE'),
  ('pkg_resources._vendor.jaraco.text',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\anaconda3\\envs\\dabao\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.recipes',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._compat',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.zipp',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\zipp.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.util',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\util.py',
   'PYMODULE'),
  ('pkg_resources._vendor.appdirs',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\appdirs.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.exceptions',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.simple',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._legacy',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._musllinux',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.diagram',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._common',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.__about__',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.helpers',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('sysconfig', 'D:\\anaconda3\\envs\\dabao\\lib\\sysconfig.py', 'PYMODULE'),
  ('_osx_support',
   'D:\\anaconda3\\envs\\dabao\\lib\\_osx_support.py',
   'PYMODULE'),
  ('distutils.log',
   'D:\\anaconda3\\envs\\dabao\\lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils',
   'D:\\anaconda3\\envs\\dabao\\lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'D:\\anaconda3\\envs\\dabao\\lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'D:\\anaconda3\\envs\\dabao\\lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.file_util',
   'D:\\anaconda3\\envs\\dabao\\lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'D:\\anaconda3\\envs\\dabao\\lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.spawn',
   'D:\\anaconda3\\envs\\dabao\\lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.debug',
   'D:\\anaconda3\\envs\\dabao\\lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.errors',
   'D:\\anaconda3\\envs\\dabao\\lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'D:\\anaconda3\\envs\\dabao\\lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'D:\\anaconda3\\envs\\dabao\\lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.util',
   'D:\\anaconda3\\envs\\dabao\\lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.filelist',
   'D:\\anaconda3\\envs\\dabao\\lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('py_compile', 'D:\\anaconda3\\envs\\dabao\\lib\\py_compile.py', 'PYMODULE'),
  ('distutils.sysconfig',
   'D:\\anaconda3\\envs\\dabao\\lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'D:\\anaconda3\\envs\\dabao\\lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\anaconda3\\envs\\dabao\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('imp', 'D:\\anaconda3\\envs\\dabao\\lib\\imp.py', 'PYMODULE'),
  ('importlib',
   'D:\\anaconda3\\envs\\dabao\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('posixpath', 'D:\\anaconda3\\envs\\dabao\\lib\\posixpath.py', 'PYMODULE'),
  ('genericpath',
   'D:\\anaconda3\\envs\\dabao\\lib\\genericpath.py',
   'PYMODULE'),
  ('ntpath', 'D:\\anaconda3\\envs\\dabao\\lib\\ntpath.py', 'PYMODULE'),
  ('inspect', 'D:\\anaconda3\\envs\\dabao\\lib\\inspect.py', 'PYMODULE'),
  ('token', 'D:\\anaconda3\\envs\\dabao\\lib\\token.py', 'PYMODULE'),
  ('textwrap', 'D:\\anaconda3\\envs\\dabao\\lib\\textwrap.py', 'PYMODULE'),
  ('tempfile', 'D:\\anaconda3\\envs\\dabao\\lib\\tempfile.py', 'PYMODULE'),
  ('email.parser',
   'D:\\anaconda3\\envs\\dabao\\lib\\email\\parser.py',
   'PYMODULE'),
  ('plistlib', 'D:\\anaconda3\\envs\\dabao\\lib\\plistlib.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\anaconda3\\envs\\dabao\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\anaconda3\\envs\\dabao\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml', 'D:\\anaconda3\\envs\\dabao\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\anaconda3\\envs\\dabao\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\anaconda3\\envs\\dabao\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\anaconda3\\envs\\dabao\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('getpass', 'D:\\anaconda3\\envs\\dabao\\lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'D:\\anaconda3\\envs\\dabao\\lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'D:\\anaconda3\\envs\\dabao\\lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'D:\\anaconda3\\envs\\dabao\\lib\\netrc.py', 'PYMODULE'),
  ('http.cookiejar',
   'D:\\anaconda3\\envs\\dabao\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('urllib.response',
   'D:\\anaconda3\\envs\\dabao\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'D:\\anaconda3\\envs\\dabao\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\anaconda3\\envs\\dabao\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\anaconda3\\envs\\dabao\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\anaconda3\\envs\\dabao\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\anaconda3\\envs\\dabao\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('pkgutil', 'D:\\anaconda3\\envs\\dabao\\lib\\pkgutil.py', 'PYMODULE'),
  ('stat', 'D:\\anaconda3\\envs\\dabao\\lib\\stat.py', 'PYMODULE'),
  ('zipfile', 'D:\\anaconda3\\envs\\dabao\\lib\\zipfile.py', 'PYMODULE'),
  ('pathlib', 'D:\\anaconda3\\envs\\dabao\\lib\\pathlib.py', 'PYMODULE'),
  ('pypinyin.seg',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pypinyin\\seg\\__init__.py',
   'PYMODULE'),
  ('pypinyin.contrib.mmseg',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pypinyin\\contrib\\mmseg.py',
   'PYMODULE'),
  ('pypinyin.phrases_dict_large',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pypinyin\\phrases_dict_large.py',
   'PYMODULE'),
  ('pypinyin.style.cyrillic',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pypinyin\\style\\cyrillic.py',
   'PYMODULE'),
  ('pypinyin.contrib.tone_sandhi',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pypinyin\\contrib\\tone_sandhi.py',
   'PYMODULE'),
  ('pypinyin.contrib',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pypinyin\\contrib\\__init__.py',
   'PYMODULE'),
  ('pypinyin.seg.simpleseg',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pypinyin\\seg\\simpleseg.py',
   'PYMODULE'),
  ('pypinyin.phrases_dict',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pypinyin\\phrases_dict.py',
   'PYMODULE'),
  ('pypinyin.style.tone',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pypinyin\\style\\tone.py',
   'PYMODULE'),
  ('pypinyin.phonetic_symbol',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pypinyin\\phonetic_symbol.py',
   'PYMODULE'),
  ('pypinyin.style.finals',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pypinyin\\style\\finals.py',
   'PYMODULE'),
  ('pypinyin.contrib.tone_convert',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pypinyin\\contrib\\tone_convert.py',
   'PYMODULE'),
  ('pypinyin.style.initials',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pypinyin\\style\\initials.py',
   'PYMODULE'),
  ('pypinyin.style.wadegiles',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pypinyin\\style\\wadegiles.py',
   'PYMODULE'),
  ('pypinyin.style',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pypinyin\\style\\__init__.py',
   'PYMODULE'),
  ('pypinyin.style.others',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pypinyin\\style\\others.py',
   'PYMODULE'),
  ('pypinyin.core',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pypinyin\\core.py',
   'PYMODULE'),
  ('pypinyin.compat',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pypinyin\\compat.py',
   'PYMODULE'),
  ('pypinyin.converter',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pypinyin\\converter.py',
   'PYMODULE'),
  ('pypinyin.utils',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pypinyin\\utils.py',
   'PYMODULE'),
  ('pypinyin.contrib.neutral_tone',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pypinyin\\contrib\\neutral_tone.py',
   'PYMODULE'),
  ('pypinyin.standard',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pypinyin\\standard.py',
   'PYMODULE'),
  ('pypinyin.seg.mmseg',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pypinyin\\seg\\mmseg.py',
   'PYMODULE'),
  ('pypinyin.__main__',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pypinyin\\__main__.py',
   'PYMODULE'),
  ('pypinyin.runner',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pypinyin\\runner.py',
   'PYMODULE'),
  ('pypinyin.style._utils',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pypinyin\\style\\_utils.py',
   'PYMODULE'),
  ('pypinyin.pinyin_dict',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pypinyin\\pinyin_dict.py',
   'PYMODULE'),
  ('pypinyin.style._constants',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pypinyin\\style\\_constants.py',
   'PYMODULE'),
  ('pypinyin.contrib.uv',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pypinyin\\contrib\\uv.py',
   'PYMODULE'),
  ('pypinyin.contrib._tone_rule',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pypinyin\\contrib\\_tone_rule.py',
   'PYMODULE'),
  ('pypinyin.style.bopomofo',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pypinyin\\style\\bopomofo.py',
   'PYMODULE'),
  ('pypinyin.constants',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pypinyin\\constants.py',
   'PYMODULE'),
  ('et_xmlfile',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\anaconda3\\envs\\dabao\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\anaconda3\\envs\\dabao\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\anaconda3\\envs\\dabao\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\anaconda3\\envs\\dabao\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree',
   'D:\\anaconda3\\envs\\dabao\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.smart_tag',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\worksheet\\smart_tag.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.worksheet.ole',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\worksheet\\ole.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.packaging.interface',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\packaging\\interface.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('decimal', 'D:\\anaconda3\\envs\\dabao\\lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\anaconda3\\envs\\dabao\\lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars',
   'D:\\anaconda3\\envs\\dabao\\lib\\contextvars.py',
   'PYMODULE'),
  ('numbers', 'D:\\anaconda3\\envs\\dabao\\lib\\numbers.py', 'PYMODULE'),
  ('openpyxl.utils.escape',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.worksheet.errors',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\worksheet\\errors.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.utils.dataframe',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\utils\\dataframe.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.controls',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\worksheet\\controls.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.utils.inference',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\utils\\inference.py',
   'PYMODULE'),
  ('openpyxl.descriptors.slots',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\descriptors\\slots.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.compat.abc',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\compat\\abc.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.picture',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\worksheet\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.singleton',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\compat\\singleton.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.product',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\compat\\product.py',
   'PYMODULE'),
  ('openpyxl.worksheet.custom',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\worksheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_watch',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\worksheet\\cell_watch.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('numpy',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('six', 'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\six.py', 'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.compat._inspect',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\compat\\_inspect.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('_dummy_thread',
   'D:\\anaconda3\\envs\\dabao\\lib\\_dummy_thread.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.ma',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.random',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.fft',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.linalg',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.lib',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.core.records',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core.machar',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\core\\machar.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.version',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy.compat',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.core',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._version',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\_version.py',
   'PYMODULE'),
  ('numpy.__config__',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._globals',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('pytz',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('pytz.lazy',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pandas.tests.series.test_ufunc',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\test_ufunc.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_astype',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.arrays.period.test_constructors',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\period\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_delete',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_delete.py',
   'PYMODULE'),
  ('pandas._libs',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_arithmetic',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.extension.arrow',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_nunique',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_nunique.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_misc',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_misc.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_count_with_level_deprecated',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_count_with_level_deprecated.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_timedelta64',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arithmetic\\test_timedelta64.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_chaining_and_caching',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexing\\test_chaining_and_caching.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_reindex_like',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_reindex_like.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.tests.internals.test_managers',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\internals\\test_managers.py',
   'PYMODULE'),
  ('pandas.tests.util.test_util',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\util\\test_util.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_series',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\plotting\\test_series.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.style',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\style.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.converter',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\converter.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.parser',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('fractions', 'D:\\anaconda3\\envs\\dabao\\lib\\fractions.py', 'PYMODULE'),
  ('dateutil.tz',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.easter',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil._version',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil._common',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_shift',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_shift.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_constructors',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_coercion',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexing\\test_coercion.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_join',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_integrity',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_integrity.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\indexing\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.string_',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\string_\\__init__.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.tests.tools.test_to_numeric',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tools\\test_to_numeric.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_cut',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\reshape\\test_cut.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_indexing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_indexing.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.tests.test_optional_dependency',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\test_optional_dependency.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_fiscal',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_fiscal.py',
   'PYMODULE'),
  ('pandas.tests.indexing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexing\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_custom_business_hour',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_custom_business_hour.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_dict_compat',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_dict_compat.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_repr',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_repr.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_timezones',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_timezones.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.tests.base.test_conversion',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\base\\test_conversion.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_reshape',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_reshape.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.dtype',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\dtype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_fillna',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_fillna.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_take',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_take.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.conftest',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\integer\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\reshape\\merge\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.series.accessors.test_dt_accessor',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\accessors\\test_dt_accessor.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_copy',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_copy.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_subclass',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\test_subclass.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.tests.arrays.period',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\period\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_searchsorted',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_searchsorted.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_sorting',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_sorting.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.common',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arithmetic\\common.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\reshape\\concat\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_setitem',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_setitem.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_pipe',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\groupby\\test_pipe.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class.test_constructors',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\base_class\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_get_set',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_get_set.py',
   'PYMODULE'),
  ('pandas.tests.frame.constructors',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\constructors\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_dtypes',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_week',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_week.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_unique',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_unique.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.tools',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\tools.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.compat',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\compat.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.misc',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\misc.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.hist',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\hist.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.core',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\core.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.timeseries',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\timeseries.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.boxplot',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\plotting\\_matplotlib\\boxplot.py',
   'PYMODULE'),
  ('pandas.tests.groupby.transform.test_transform',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\groupby\\transform\\test_transform.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.errors',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.util._test_decorators',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\util\\_test_decorators.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_iteration',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\test_iteration.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_isin',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_isin.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.tests.scalar.test_na_scalar',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\scalar\\test_na_scalar.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\dtypes\\cast\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.libs.test_lib',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\libs\\test_lib.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_indexing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexing\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.ranges.test_join',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\ranges\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_astype',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_datetimelike',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_datetimelike.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_api',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\test_api.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_convert_dtypes',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_convert_dtypes.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_astype',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.arrays.interval',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\interval\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.common',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tseries\\offsets\\common.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_tz_convert',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_tz_convert.py',
   'PYMODULE'),
  ('pandas.tests.reductions.test_reductions',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\reductions\\test_reductions.py',
   'PYMODULE'),
  ('pandas.tests.scalar.period.test_period',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\scalar\\period\\test_period.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.methods.test_fillna',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\methods\\test_fillna.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_shift',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_shift.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_formats',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_formats.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'D:\\anaconda3\\envs\\dabao\\lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'D:\\anaconda3\\envs\\dabao\\lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'D:\\anaconda3\\envs\\dabao\\lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'D:\\anaconda3\\envs\\dabao\\lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'D:\\anaconda3\\envs\\dabao\\lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'D:\\anaconda3\\envs\\dabao\\lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'D:\\anaconda3\\envs\\dabao\\lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom',
   'D:\\anaconda3\\envs\\dabao\\lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_npfuncs',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_npfuncs.py',
   'PYMODULE'),
  ('pandas.tests.resample.conftest',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\resample\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_construction',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_construction.py',
   'PYMODULE'),
  ('pandas.tests.computation.test_eval',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\computation\\test_eval.py',
   'PYMODULE'),
  ('pandas.tests.resample.test_base',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\resample\\test_base.py',
   'PYMODULE'),
  ('pandas.tests.groupby',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.methods.test_shift',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\methods\\test_shift.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.test_concat',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\dtypes\\test_concat.py',
   'PYMODULE'),
  ('pandas.tests.series.test_reductions',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\test_reductions.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_integer',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\test_integer.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.base.test_value_counts',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\base\\test_value_counts.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.test_unary_ops',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\test_unary_ops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_any_index',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\test_any_index.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_fillna',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_fillna.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_.test_indexing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.arrays.timedeltas.test_reductions',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\timedeltas\\test_reductions.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arithmetic\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.numeric',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\indexes\\numeric.py',
   'PYMODULE'),
  ('pandas.tests.tseries.holiday',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tseries\\holiday\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.test_algos',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\test_algos.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.tests.scalar',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\scalar\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_set_value',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\indexing\\test_set_value.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_block_internals',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\test_block_internals.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_duplicates',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_duplicates.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_series_transform',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\apply\\test_series_transform.py',
   'PYMODULE'),
  ('pandas.tests.window',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_duplicated',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_duplicated.py',
   'PYMODULE'),
  ('pandas.tests.indexes.ranges',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\ranges\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.base.test_transpose',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\base\\test_transpose.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_align',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_align.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_concat',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_concat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.ranges.test_range',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\ranges\\test_range.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_factorize',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_factorize.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_infer_datetimelike',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_infer_datetimelike.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_equals',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_equals.py',
   'PYMODULE'),
  ('pandas.tests.arrays.masked.test_arithmetic',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\masked\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_index_new',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\test_index_new.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_base',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\test_base.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_pop',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_pop.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_subclass',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_subclass.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_get_numeric_data',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_get_numeric_data.py',
   'PYMODULE'),
  ('pandas.tests.arrays.test_numpy',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\test_numpy.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.test_timestamp',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\test_timestamp.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_arithmetic',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.util',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.extension.arrow.test_bool',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\arrow\\test_bool.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_compare',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_compare.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_count',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_count.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_validate',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\test_validate.py',
   'PYMODULE'),
  ('pandas.util.testing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\util\\testing.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_backend',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\plotting\\test_backend.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_category',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_category.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_allowlist',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\groupby\\test_allowlist.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_scalar_compat',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\period\\test_scalar_compat.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_dtypes',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_timedelta_range',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_timedelta_range.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_promote',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_promote.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_find_replace',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\strings\\test_find_replace.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_extension_array_equal',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\util\\test_assert_extension_array_equal.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_datetime',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_datetime.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_libsparse',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_libsparse.py',
   'PYMODULE'),
  ('pandas.tests.tseries.frequencies.test_freq_code',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tseries\\frequencies\\test_freq_code.py',
   'PYMODULE'),
  ('pandas.tests.plotting',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_categorical',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\groupby\\test_categorical.py',
   'PYMODULE'),
  ('pandas.tests.generic.test_duplicate_labels',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\generic\\test_duplicate_labels.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('csv', 'D:\\anaconda3\\envs\\dabao\\lib\\csv.py', 'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.tests.strings.conftest',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\strings\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_at',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexing\\test_at.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_setops',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_setops.py',
   'PYMODULE'),
  ('pandas.tests.groupby.aggregate.test_aggregate',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\groupby\\aggregate\\test_aggregate.py',
   'PYMODULE'),
  ('pandas.tests.indexing.common',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexing\\common.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_api',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\strings\\test_api.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_invalid_arg',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\apply\\test_invalid_arg.py',
   'PYMODULE'),
  ('pandas.tests.api',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_first_and_last',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_first_and_last.py',
   'PYMODULE'),
  ('pandas.tests.series',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_repeat',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_repeat.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_almost_equal',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\util\\test_assert_almost_equal.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_partial',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexing\\test_partial.py',
   'PYMODULE'),
  ('pandas.tests.libs',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\libs\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_rank',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_rank.py',
   'PYMODULE'),
  ('pandas.tests.tools',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_cat',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\strings\\test_cat.py',
   'PYMODULE'),
  ('pandas.tests.extension.arrow.arrays',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\arrow\\arrays.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_to_period',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_to_period.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_sort',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_sort.py',
   'PYMODULE'),
  ('pandas.tests.test_common',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\test_common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_to_timestamp',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_to_timestamp.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_construct_ndarray',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_construct_ndarray.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_swapaxes',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_swapaxes.py',
   'PYMODULE'),
  ('pandas.tests.extension.decimal.test_decimal',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\decimal\\test_decimal.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_partial_indexing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_partial_indexing.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.plotting',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_string_array',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\strings\\test_string_array.py',
   'PYMODULE'),
  ('pandas.compat.chainmap',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\compat\\chainmap.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_accessor',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_accessor.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_getitem',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_getitem.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_datetime64',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arithmetic\\test_datetime64.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_construction',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_construction.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_between',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_between.py',
   'PYMODULE'),
  ('pandas.tests.util.conftest',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\util\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_repr_info',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\test_repr_info.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_business_hour',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_business_hour.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.conftest',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\categorical\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_to_records',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_to_records.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_asof',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_asof.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_take',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_take.py',
   'PYMODULE'),
  ('pandas.tests.tools.test_to_time',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tools\\test_to_time.py',
   'PYMODULE'),
  ('pandas._typing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_ufunc',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\test_ufunc.py',
   'PYMODULE'),
  ('pandas.tests.arrays.interval.test_astype',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\interval\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_drop',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_drop.py',
   'PYMODULE'),
  ('pandas.tests.base.test_misc',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\base\\test_misc.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\categorical\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.tseries',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_categorical',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arithmetic\\test_categorical.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_produces_warning',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\util\\test_assert_produces_warning.py',
   'PYMODULE'),
  ('pandas.tests.groupby.conftest',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\groupby\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_interpolate',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_interpolate.py',
   'PYMODULE'),
  ('pandas.tests.util.test_validate_args_and_kwargs',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\util\\test_validate_args_and_kwargs.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tseries\\offsets\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.test_errors',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\test_errors.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class.test_reshape',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\base_class\\test_reshape.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_nlargest',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_nlargest.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_pct_change',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_pct_change.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_categorical',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexing\\test_categorical.py',
   'PYMODULE'),
  ('pandas.tests.indexes.conftest',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_at_time',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_at_time.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_function',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\groupby\\test_function.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_count',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_count.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_formats',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\period\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.extension.json',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_numpy',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\test_numpy.py',
   'PYMODULE'),
  ('pandas.tests.construction',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\construction\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_dot',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_dot.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_getitem',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\indexing\\test_getitem.py',
   'PYMODULE'),
  ('pandas.tests.indexes.ranges.test_indexing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\ranges\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_any_all',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\groupby\\test_any_all.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_apply_mutate',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\groupby\\test_apply_mutate.py',
   'PYMODULE'),
  ('pandas.tests.series.methods',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_delitem',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\indexing\\test_delitem.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_first_valid_index',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_first_valid_index.py',
   'PYMODULE'),
  ('pandas.tests.base.test_fillna',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\base\\test_fillna.py',
   'PYMODULE'),
  ('pandas.tests.test_sorting',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\test_sorting.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_construct_object_arr',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_construct_object_arr.py',
   'PYMODULE'),
  ('pandas.tests.extension.decimal',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\decimal\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_frame_equal',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\util\\test_assert_frame_equal.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_timedelta',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_timedelta.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.describe',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\describe.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_partial',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_partial.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_timedeltas',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tslibs\\test_timedeltas.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_common',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\test_common.py',
   'PYMODULE'),
  ('pandas.core.util',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_tools',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\period\\test_tools.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_interval',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\test_interval.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_to_frame',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_to_frame.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_append',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_append.py',
   'PYMODULE'),
  ('pandas.tests.series.test_constructors',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_sort_index',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_sort_index.py',
   'PYMODULE'),
  ('pandas.tests.config.test_config',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\config\\test_config.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_split_partition',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\strings\\test_split_partition.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_align',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_align.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_mask',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\indexing\\test_mask.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_nunique',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\groupby\\test_nunique.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_rank',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_rank.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_counting',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\groupby\\test_counting.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas.tests.generic.test_finalize',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\generic\\test_finalize.py',
   'PYMODULE'),
  ('pandas.core.aggregation',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\aggregation.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_arithmetic',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.series.test_api',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\test_api.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_rename_axis',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_rename_axis.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_get_dummies',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\reshape\\test_get_dummies.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_drop',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_drop.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.io',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_yqm_offsets',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_yqm_offsets.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_lookup',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_lookup.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_filters',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\groupby\\test_filters.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_is_monotonic',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_is_monotonic.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_frozen',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\test_frozen.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_constructors',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_constructors.py',
   'PYMODULE'),
  ('pandas._config.display',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas.tests.config.test_localization',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\config\\test_localization.py',
   'PYMODULE'),
  ('pandas.util._depr_module',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\util\\_depr_module.py',
   'PYMODULE'),
  ('pandas.tests.groupby.aggregate.test_cython',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\groupby\\aggregate\\test_cython.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.methods.test_repeat',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\methods\\test_repeat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_.test_sort_values',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\test_sort_values.py',
   'PYMODULE'),
  ('pandas._testing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_describe',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_describe.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_union_categoricals',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\reshape\\test_union_categoricals.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_reindex',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_reindex.py',
   'PYMODULE'),
  ('pandas.core.sparse.api',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\sparse\\api.py',
   'PYMODULE'),
  ('pandas.tests.indexing.interval.test_interval',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexing\\interval\\test_interval.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_utils',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\arrays\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.tests.libs.test_join',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\libs\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_frame_apply',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\apply\\test_frame_apply.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_get_numeric_data',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_get_numeric_data.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_indexing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\period\\test_indexing.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_pct_change',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_pct_change.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_ops',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\period\\test_ops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_sorting',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_sorting.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_compare',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_compare.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_frame_transform',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\apply\\test_frame_transform.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_ops',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_ops.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_index_as_string',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\groupby\\test_index_as_string.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_concat',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_concat.py',
   'PYMODULE'),
  ('pandas.tests.series.test_npfuncs',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\test_npfuncs.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_to_series',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_to_series.py',
   'PYMODULE'),
  ('pandas.tests.groupby.aggregate.test_numba',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\groupby\\aggregate\\test_numba.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_datetime',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\test_datetime.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_loc',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_loc.py',
   'PYMODULE'),
  ('pandas.tests.series.accessors.test_sparse_accessor',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\accessors\\test_sparse_accessor.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_pivot_multilevel',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\reshape\\test_pivot_multilevel.py',
   'PYMODULE'),
  ('pandas.tests.extension.arrow.test_string',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\arrow\\test_string.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_dropna',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_dropna.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timedelta.test_timedelta',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\scalar\\timedelta\\test_timedelta.py',
   'PYMODULE'),
  ('pandas.core.api',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_libfrequencies',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tslibs\\test_libfrequencies.py',
   'PYMODULE'),
  ('pandas.tests.arrays.masked.test_arrow_compat',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\masked\\test_arrow_compat.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_operators',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_operators.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_parse_iso8601',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tslibs\\test_parse_iso8601.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_matmul',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_matmul.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.tests.indexes.object.test_astype',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\object\\test_astype.py',
   'PYMODULE'),
  ('pandas.core.sparse',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_round',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_round.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_truncate',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_truncate.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_reset_index',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_reset_index.py',
   'PYMODULE'),
  ('pandas.tests.scalar.test_nat',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\scalar\\test_nat.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_month',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_month.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.common',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\categorical\\common.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.tests.plotting.frame.test_frame_color',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\plotting\\frame\\test_frame_color.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_get',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\indexing\\test_get.py',
   'PYMODULE'),
  ('pandas.tests.tseries.holiday.test_observance',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tseries\\holiday\\test_observance.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge.test_merge_ordered',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\reshape\\merge\\test_merge_ordered.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_ccalendar',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tslibs\\test_ccalendar.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_setops',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_setops.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.tests.series.test_validate',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\test_validate.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timedelta.test_constructors',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\scalar\\timedelta\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.plotting.frame.test_frame',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\plotting\\frame\\test_frame.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_append',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_append.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_datetime',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\indexing\\test_datetime.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.methods.test_factorize',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\methods\\test_factorize.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_item',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_item.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_replace',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_replace.py',
   'PYMODULE'),
  ('pandas.tests.indexing.interval.test_interval_new',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexing\\interval\\test_interval_new.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_nonunique_indexes',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\test_nonunique_indexes.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_period',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arithmetic\\test_period.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_converter',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\plotting\\test_converter.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_asof',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_asof.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_construction',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_construction.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.tests.extension.json.test_json',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\json\\test_json.py',
   'PYMODULE'),
  ('pandas.tests.util.test_deprecate',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\util\\test_deprecate.py',
   'PYMODULE'),
  ('pandas._config.config',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_setitem',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_setitem.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_take',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_take.py',
   'PYMODULE'),
  ('pandas.arrays',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas._config',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_values',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_values.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_fillna',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_fillna.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_stack_unstack',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\test_stack_unstack.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_base',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_base.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_insert',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_insert.py',
   'PYMODULE'),
  ('pandas.core.window.indexers',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\window\\indexers.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_combine',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_combine.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.generic.test_generic',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\generic\\test_generic.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.conftest',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\multi\\conftest.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_melt',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\reshape\\test_melt.py',
   'PYMODULE'),
  ('pandas.io',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_libgroupby',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\groupby\\test_libgroupby.py',
   'PYMODULE'),
  ('pandas.tseries',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_convert',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_convert.py',
   'PYMODULE'),
  ('pandas.tests.extension.list.test_list',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\list\\test_list.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_dtype',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_dtype.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_concat',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_concat.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_algos',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_algos.py',
   'PYMODULE'),
  ('pandas.tests.reductions',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\reductions\\__init__.py',
   'PYMODULE'),
  ('pandas.core.base',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_mask',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_mask.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_replace',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_replace.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timedelta.test_arithmetic',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\scalar\\timedelta\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_nlargest',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_nlargest.py',
   'PYMODULE'),
  ('pandas.tests.base.test_constructors',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\base\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.arrays.timedeltas.test_constructors',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\timedeltas\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_join',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.test_nanops',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\test_nanops.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.tests.resample.test_time_grouper',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\resample\\test_time_grouper.py',
   'PYMODULE'),
  ('pandas.compat',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.tests.arrays.interval.test_interval',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\interval\\test_interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_common',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\test_common.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_conversion',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tslibs\\test_conversion.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_explode',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_explode.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.tests.test_register_accessor',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\test_register_accessor.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_shift',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_shift.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_util',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\reshape\\test_util.py',
   'PYMODULE'),
  ('pandas.tests.tseries.frequencies',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tseries\\frequencies\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.test_arithmetic',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.indexes.numeric.test_join',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\numeric\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.test_downstream',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\test_downstream.py',
   'PYMODULE'),
  ('pandas.tests.util',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_unstack',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_unstack.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_monotonic',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\period\\test_monotonic.py',
   'PYMODULE'),
  ('pandas.tests.base.test_unique',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\base\\test_unique.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_value_counts',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\groupby\\test_value_counts.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_numpy_array_equal',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\util\\test_assert_numpy_array_equal.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_groupby_dropna',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\groupby\\test_groupby_dropna.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_floats',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexing\\test_floats.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_min_max',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\groupby\\test_min_max.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_to_offset',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tslibs\\test_to_offset.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_indexing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_arithmetic',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.tests.scalar.interval.test_ops',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\scalar\\interval\\test_ops.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.resample.test_deprecated',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\resample\\test_deprecated.py',
   'PYMODULE'),
  ('pandas.tests.indexes.numeric.test_setops',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\numeric\\test_setops.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.object.test_indexing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\object\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_missing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_missing.py',
   'PYMODULE'),
  ('pandas.io.html',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.tests.util.test_hashing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\util\\test_hashing.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas.testing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_analytics',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_analytics.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_asfreq',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_asfreq.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_qcut',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\reshape\\test_qcut.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_groupby',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\plotting\\test_groupby.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\interval\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_timegrouper',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\groupby\\test_timegrouper.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_reductions',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\test_reductions.py',
   'PYMODULE'),
  ('pandas.tests.dtypes',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_sorted',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_sorted.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_formats',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_cumulative',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\test_cumulative.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_setops',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\test_setops.py',
   'PYMODULE'),
  ('pandas.tests.frame.conftest',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_combine_first',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_combine_first.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_astype',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_swaplevel',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_swaplevel.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class.test_indexing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\base_class\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_logical_ops',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\test_logical_ops.py',
   'PYMODULE'),
  ('pandas.core.series',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas.tests.groupby.transform',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\groupby\\transform\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_extension',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\test_extension.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.tests.extension.base',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\base\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.setitem',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\base\\setitem.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.base',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\base\\base.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.reshaping',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\base\\reshaping.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.reduce',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\base\\reduce.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.printing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\base\\printing.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.ops',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\base\\ops.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.missing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\base\\missing.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.methods',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\base\\methods.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.io',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\base\\io.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.interface',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\base\\interface.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.groupby',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\base\\groupby.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.getitem',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\base\\getitem.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.dtype',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\base\\dtype.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.dim2',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\base\\dim2.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.constructors',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\base\\constructors.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.casting',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\base\\casting.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.test_timezones',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\test_timezones.py',
   'PYMODULE'),
  ('pandas.tests.scalar.period.test_asfreq',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\scalar\\period\\test_asfreq.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_set_index',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_set_index.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_drop',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_drop.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_common',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\plotting\\test_common.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_reduction',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_reduction.py',
   'PYMODULE'),
  ('pandas.api',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_chaining_and_caching',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_chaining_and_caching.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_series_apply',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\apply\\test_series_apply.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_extract',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\strings\\test_extract.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_constructors',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_partial_slicing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\period\\test_partial_slicing.py',
   'PYMODULE'),
  ('pandas.tests.apply',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\apply\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_indexing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\indexing\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_datetimes',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_datetimes.py',
   'PYMODULE'),
  ('pandas.tests.scalar.period',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\scalar\\period\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_between_time',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_between_time.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_period_range',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\period\\test_period_range.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_function',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_function.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_quantile',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\groupby\\test_quantile.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_join',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.extension.list',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\list\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_monotonic',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_monotonic.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_missing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\groupby\\test_missing.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_transpose',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_transpose.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_categorical_equal',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\util\\test_assert_categorical_equal.py',
   'PYMODULE'),
  ('pandas.tests.series.accessors.test_str_accessor',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\accessors\\test_str_accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.period.test_astype',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\period\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_analytics',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_analytics.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_date_range',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_date_range.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_grouping',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\groupby\\test_grouping.py',
   'PYMODULE'),
  ('pandas.tests.arrays.test_datetimelike',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\test_datetimelike.py',
   'PYMODULE'),
  ('pandas.tests.strings',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_datetime',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexing\\test_datetime.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_repr',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_repr.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_take',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\indexing\\test_take.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_partial_slicing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_partial_slicing.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_droplevel',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_droplevel.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_asfreq',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_asfreq.py',
   'PYMODULE'),
  ('pandas.core.window',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_series_equal',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\util\\test_assert_series_equal.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_dtypes',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_reindex',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_reindex.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_get',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_get.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_find_common_type',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_find_common_type.py',
   'PYMODULE'),
  ('pandas.conftest',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_boolean',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\test_boolean.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_string',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\test_string.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge.test_join',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\reshape\\merge\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_to_csv',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_to_csv.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\period\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_cov_corr',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_cov_corr.py',
   'PYMODULE'),
  ('pandas.tests.indexes.numeric.test_indexing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\numeric\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.test_take',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\test_take.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_autocorr',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_autocorr.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_slice',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_slice.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.tests.series.test_unary',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\test_unary.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_fields',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tslibs\\test_fields.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_interval',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arithmetic\\test_interval.py',
   'PYMODULE'),
  ('pandas.tests.base.common',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\base\\common.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_clip',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_clip.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_xs',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\indexing\\test_xs.py',
   'PYMODULE'),
  ('pandas.tests.computation.test_compat',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\computation\\test_compat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.methods',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_rename',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_rename.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_infer_objects',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_infer_objects.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_lexsort',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_lexsort.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_append',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_append.py',
   'PYMODULE'),
  ('pandas.tests.tseries.holiday.test_holiday',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tseries\\holiday\\test_holiday.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_astype',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_is_full',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_is_full.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_map',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_map.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.tests.apply.common',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\apply\\common.py',
   'PYMODULE'),
  ('pandas.io.formats.latex',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\formats\\latex.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.tests.extension.list.array',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\list\\array.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_npfuncs',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\test_npfuncs.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_.test_value_counts',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\test_value_counts.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_add_prefix_suffix',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_add_prefix_suffix.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_fillna',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_fillna.py',
   'PYMODULE'),
  ('pandas.core.common',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_isna',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_isna.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_update',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_update.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_combine_concat',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_combine_concat.py',
   'PYMODULE'),
  ('pandas.tests.apply.conftest',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\apply\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_to_numpy',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_to_numpy.py',
   'PYMODULE'),
  ('pandas.tests.tseries.frequencies.test_inference',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tseries\\frequencies\\test_inference.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_astype',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_astype.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_scalar_compat',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_scalar_compat.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.tests.series.test_missing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\test_missing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_join',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\period\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_series_apply_relabeling',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\apply\\test_series_apply_relabeling.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_argsort',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_argsort.py',
   'PYMODULE'),
  ('pandas.tests.indexes.common',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\common.py',
   'PYMODULE'),
  ('pandas.tests.indexes',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.datetimes.test_reductions',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\datetimes\\test_reductions.py',
   'PYMODULE'),
  ('pandas.tests.arrays.period.test_reductions',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\period\\test_reductions.py',
   'PYMODULE'),
  ('pandas.tests.api.test_types',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\api\\test_types.py',
   'PYMODULE'),
  ('pandas.tests.series.accessors.test_cat_accessor',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\accessors\\test_cat_accessor.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_nth',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\groupby\\test_nth.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_diff',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_diff.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_scalar_compat',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_scalar_compat.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_maybe_box_native',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_maybe_box_native.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_engines',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\test_engines.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_snap',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_snap.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.tests.arrays.test_timedeltas',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\test_timedeltas.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_groupby_subclass',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\groupby\\test_groupby_subclass.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_is_homogeneous_dtype',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_is_homogeneous_dtype.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_empty',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_empty.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.conftest',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\reshape\\concat\\conftest.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.tests.arrays.timedeltas',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\timedeltas\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class.test_setops',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\base_class\\test_setops.py',
   'PYMODULE'),
  ('pandas.io.date_converters',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\date_converters.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.tests.scalar.interval',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\scalar\\interval\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.internals.test_api',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\internals\\test_api.py',
   'PYMODULE'),
  ('pandas.tests.test_aggregation',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\test_aggregation.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_rename_axis',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_rename_axis.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_categorical',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\test_categorical.py',
   'PYMODULE'),
  ('pandas.tests.arrays.datetimes.test_constructors',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\datetimes\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.resample.test_timedelta',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\resample\\test_timedelta.py',
   'PYMODULE'),
  ('pandas.tests.series.test_arithmetic',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_formats',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_value_counts',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_value_counts.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_replace',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_replace.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\indexing\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_indexing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_indexing.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_bin_groupby',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\groupby\\test_bin_groupby.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_value_counts',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_value_counts.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_to_dict',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_to_dict.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_tz_localize',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_tz_localize.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_set_axis',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_set_axis.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_isin',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_isin.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_where',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_where.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_function',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_function.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_indexing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.generic',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\generic\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_dst',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_dst.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.extension.conftest',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_describe',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_describe.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_attr_equal',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\util\\test_assert_attr_equal.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_case_justify',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\strings\\test_case_justify.py',
   'PYMODULE'),
  ('pandas.tests.extension.arrow.test_timestamp',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\arrow\\test_timestamp.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.methods.test_insert',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\methods\\test_insert.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas._version',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_setitem',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\indexing\\test_setitem.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_hist_method',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\plotting\\test_hist_method.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_setops',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\period\\test_setops.py',
   'PYMODULE'),
  ('pandas.tests.util.test_show_versions',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\util\\test_show_versions.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_indexing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_iloc',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexing\\test_iloc.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_select_dtypes',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_select_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_combine',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_combine.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.tests.frame',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_head_tail',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_head_tail.py',
   'PYMODULE'),
  ('pandas.tests.extension.json.array',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\json\\array.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_assign',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_assign.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_index',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_index.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_append_common',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_append_common.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.conftest',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\floating\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_diff',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_diff.py',
   'PYMODULE'),
  ('pandas.tests.arrays.datetimes',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\datetimes\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_set_name',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_set_name.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.resample.test_datetime_index',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\resample\\test_datetime_index.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_logical',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_logical.py',
   'PYMODULE'),
  ('pandas.tests.arrays.masked.test_function',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\masked\\test_function.py',
   'PYMODULE'),
  ('pandas.tests.api.test_api',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\api\\test_api.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_xs',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_xs.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_comparison',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_comparison.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_opening_times',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_opening_times.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_groupby',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\groupby\\test_groupby.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge.test_merge',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\reshape\\merge\\test_merge.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_apply',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\groupby\\test_apply.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_unary',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\test_unary.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_alter_axes',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\test_alter_axes.py',
   'PYMODULE'),
  ('pandas.tests.arrays.test_ndarray_backed',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\test_ndarray_backed.py',
   'PYMODULE'),
  ('pandas.tests.computation',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_comparison',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_comparison.py',
   'PYMODULE'),
  ('pandas.tests.base',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\base\\__init__.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.tests.scalar.interval.test_arithmetic',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\scalar\\interval\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.tests.series.test_cumulative',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\test_cumulative.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.test_dtypes',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\dtypes\\test_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_period_asfreq',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tslibs\\test_period_asfreq.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_interval_range',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_interval_range.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_numeric',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arithmetic\\test_numeric.py',
   'PYMODULE'),
  ('pandas.tests.generic.test_series',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\generic\\test_series.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_append',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_append.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_dtypes',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_insert',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_insert.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_iat',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexing\\test_iat.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_ticks',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_ticks.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_to_period',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_to_period.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.test_common',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\dtypes\\test_common.py',
   'PYMODULE'),
  ('pandas.tests.arrays.test_period',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\test_period.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_datetimelike',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\plotting\\test_datetimelike.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_query_eval',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\test_query_eval.py',
   'PYMODULE'),
  ('pandas.tests.plotting.frame.test_frame_groupby',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\plotting\\frame\\test_frame_groupby.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_period',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\period\\test_period.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_constructors',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_business_day',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_business_day.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_offsets_properties',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_offsets_properties.py',
   'PYMODULE'),
  ('pandas.tests.tseries.frequencies.test_frequencies',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tseries\\frequencies\\test_frequencies.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_style',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\plotting\\test_style.py',
   'PYMODULE'),
  ('pandas.tests.series.test_logical_ops',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\test_logical_ops.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_equals',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_equals.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_set_value',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_set_value.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_setops',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_setops.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.tests.frame.constructors.test_from_dict',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\constructors\\test_from_dict.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_infer_dtype',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_infer_dtype.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_round',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_round.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timedelta.test_formats',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\scalar\\timedelta\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_indexers',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexing\\test_indexers.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.test_constructors',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_get_value',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_get_value.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_convert_dtypes',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_convert_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_arithmetics',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_arithmetics.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\integer\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.test_multilevel',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\test_multilevel.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_warnings',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_warnings.py',
   'PYMODULE'),
  ('pandas.io.common',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_join',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\floating\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.tests.tools.test_to_timedelta',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tools\\test_to_timedelta.py',
   'PYMODULE'),
  ('pandas.tests.libs.test_hashtable',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\libs\\test_hashtable.py',
   'PYMODULE'),
  ('tracemalloc',
   'D:\\anaconda3\\envs\\dabao\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_sparse',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\test_sparse.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_cov_corr',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_cov_corr.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_is_unique',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_is_unique.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_dataframe',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_dataframe.py',
   'PYMODULE'),
  ('pandas.tests.arrays.test_array',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\test_array.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_scalar',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexing\\test_scalar.py',
   'PYMODULE'),
  ('pandas.tests.resample.test_period_index',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\resample\\test_period_index.py',
   'PYMODULE'),
  ('pandas.tests.groupby.aggregate.test_other',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\groupby\\aggregate\\test_other.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_clip',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_clip.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_constructors',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.util.test_validate_args',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\util\\test_validate_args.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_crosstab',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\reshape\\test_crosstab.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_names',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_names.py',
   'PYMODULE'),
  ('pandas.tests.groupby.aggregate',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\groupby\\aggregate\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_pivot',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\reshape\\test_pivot.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_infer_objects',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_infer_objects.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_searchsorted',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_searchsorted.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_interpolate',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_interpolate.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_reindex',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_reindex.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_invalid',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_invalid.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_interval',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_interval.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_update',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_update.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_indexing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.extension.decimal.array',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\decimal\\array.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_pop',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_pop.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_filter',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_filter.py',
   'PYMODULE'),
  ('pandas.tests.util.test_doc',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\util\\test_doc.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_iloc',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_iloc.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_pickle',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_pickle.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_to_numpy',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_to_numpy.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_shift',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_shift.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.test_generic',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\dtypes\\test_generic.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge.test_merge_index_as_string',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\reshape\\merge\\test_merge_index_as_string.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class.test_formats',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\base_class\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.tslibs',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.test_flags',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\test_flags.py',
   'PYMODULE'),
  ('pandas.tests.scalar.interval.test_interval',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\scalar\\interval\\test_interval.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge.test_merge_cross',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\reshape\\merge\\test_merge_cross.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_.test_nat',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\test_nat.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.api.types',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_searchsorted',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\period\\test_searchsorted.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.tests.frame.constructors.test_from_records',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\constructors\\test_from_records.py',
   'PYMODULE'),
  ('pandas.tests.arrays.interval.test_ops',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\interval\\test_ops.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_liboffsets',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tslibs\\test_liboffsets.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.methods.test_astype',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\methods\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.plotting.frame.test_frame_legend',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\plotting\\frame\\test_frame_legend.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\indexers.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_asof',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_asof.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_interval_array_equal',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\util\\test_assert_interval_array_equal.py',
   'PYMODULE'),
  ('pandas.tests.series.test_iteration',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\test_iteration.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('sqlite3',
   'D:\\anaconda3\\envs\\dabao\\lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'D:\\anaconda3\\envs\\dabao\\lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'D:\\anaconda3\\envs\\dabao\\lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_repeat',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_repeat.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_misc',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\plotting\\test_misc.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_multiindex',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_multiindex.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_array',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_array.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_na_indexing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexing\\test_na_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_conversion',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_conversion.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_get_dummies',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\strings\\test_get_dummies.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_copy',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_copy.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_equivalence',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_equivalence.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_rename',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_rename.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_reindex',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_reindex.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_pipe',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_pipe.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.util.test_deprecate_kwarg',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\util\\test_deprecate_kwarg.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_equals',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_equals.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_loc',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexing\\test_loc.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_unique',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_unique.py',
   'PYMODULE'),
  ('pandas.tests.series.accessors',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\accessors\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_truncate',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_truncate.py',
   'PYMODULE'),
  ('pandas.tests.util.test_deprecate_nonkeyword_arguments',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\util\\test_deprecate_nonkeyword_arguments.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_formats',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_ops',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_ops.py',
   'PYMODULE'),
  ('pandas.tests.arrays.period.test_arrow_compat',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\period\\test_arrow_compat.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_sort_values',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_sort_values.py',
   'PYMODULE'),
  ('pandas.io.json',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_getitem',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_getitem.py',
   'PYMODULE'),
  ('pandas.tests.internals.test_internals',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\internals\\test_internals.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.tests.indexes.object',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\object\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.generic.test_to_xarray',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\generic\\test_to_xarray.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_to_dict_of_blocks',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_to_dict_of_blocks.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.tests.reductions.test_stat_reductions',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\reductions\\test_stat_reductions.py',
   'PYMODULE'),
  ('pandas.tests.plotting.common',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\plotting\\common.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_map',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_map.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_quantile',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_quantile.py',
   'PYMODULE'),
  ('pandas.util.version',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_api',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tslibs\\test_api.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.conftest',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tseries\\offsets\\conftest.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_to_timestamp',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_to_timestamp.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_interval_tree',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_interval_tree.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_astype',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_astype.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.tests.util.test_safe_import',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\util\\test_safe_import.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_drop_duplicates',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_drop_duplicates.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_get_level_values',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_get_level_values.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_floating',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\test_floating.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_reset_index',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_reset_index.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.conftest',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arithmetic\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_factorize',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_factorize.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_sample',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_sample.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_timezones',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tslibs\\test_timezones.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_sort_values',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_sort_values.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_repr',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_repr.py',
   'PYMODULE'),
  ('pandas._testing._random',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\_testing\\_random.py',
   'PYMODULE'),
  ('pandas.tests.util.test_numba',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\util\\test_numba.py',
   'PYMODULE'),
  ('pandas.tests.generic.test_frame',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\generic\\test_frame.py',
   'PYMODULE'),
  ('pandas.tests.arrays.masked',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\masked\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.test_comparisons',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\test_comparisons.py',
   'PYMODULE'),
  ('pandas.tests.indexes.numeric.test_astype',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\numeric\\test_astype.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('uuid', 'D:\\anaconda3\\envs\\dabao\\lib\\uuid.py', 'PYMODULE'),
  ('ctypes.util',
   'D:\\anaconda3\\envs\\dabao\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes._aix',
   'D:\\anaconda3\\envs\\dabao\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\anaconda3\\envs\\dabao\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\anaconda3\\envs\\dabao\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\anaconda3\\envs\\dabao\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\anaconda3\\envs\\dabao\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_constructors',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\period\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_indexing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_compat',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_compat.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_frame_apply_relabeling',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\apply\\test_frame_apply_relabeling.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_external_block',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\test_external_block.py',
   'PYMODULE'),
  ('pandas.tests.resample.test_resampler_grouper',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\resample\\test_resampler_grouper.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_asfreq',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_asfreq.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_indexing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.arrays.string_.test_string_arrow',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\string_\\test_string_arrow.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_.test_equals',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\test_equals.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_duplicated',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_duplicated.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_parsing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tslibs\\test_parsing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_astype',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_astype.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_numpy_compat',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\test_numpy_compat.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_repr',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_repr.py',
   'PYMODULE'),
  ('pandas.tests.groupby.transform.test_numba',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\groupby\\transform\\test_numba.py',
   'PYMODULE'),
  ('pandas.core',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\boolean\\__init__.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_categorical',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_categorical.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_api',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_api.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_constructors',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_index_equal',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\util\\test_assert_index_equal.py',
   'PYMODULE'),
  ('pandas.io.excel._xlwt',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\excel\\_xlwt.py',
   'PYMODULE'),
  ('pandas.tests.indexing.interval',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexing\\interval\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_boxplot_method',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\plotting\\test_boxplot_method.py',
   'PYMODULE'),
  ('pandas.tests.resample.test_resample_api',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\resample\\test_resample_api.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_indexing_slow',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_indexing_slow.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\categorical\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_constructors',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge.test_merge_asof',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\reshape\\merge\\test_merge_asof.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\base_class\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_isin',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_isin.py',
   'PYMODULE'),
  ('pandas.tests.arrays.string_.test_string',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\string_\\test_string.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_comparison',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_comparison.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_size',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\groupby\\test_size.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_head_tail',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_head_tail.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timedelta',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\scalar\\timedelta\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_reorder_levels',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_reorder_levels.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.test_missing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\dtypes\\test_missing.py',
   'PYMODULE'),
  ('pandas.tests.construction.test_extract_array',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\construction\\test_extract_array.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_delitem',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_delitem.py',
   'PYMODULE'),
  ('pandas.tests.plotting.frame.test_frame_subplots',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\plotting\\frame\\test_frame_subplots.py',
   'PYMODULE'),
  ('pandas.tests.frame.common',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\common.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_.test_drop_duplicates',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\test_drop_duplicates.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_construct_from_scalar',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_construct_from_scalar.py',
   'PYMODULE'),
  ('pandas.tests.resample',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\resample\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_strings',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\strings\\test_strings.py',
   'PYMODULE'),
  ('pandas.io.api',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_tz_convert',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_tz_convert.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_setops',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_setops.py',
   'PYMODULE'),
  ('pandas.core.ops.methods',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\ops\\methods.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_where',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\indexing\\test_where.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_convert',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_convert.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.tests.series.test_repr',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\test_repr.py',
   'PYMODULE'),
  ('pandas.tests.internals',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_quantile',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_insert',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_insert.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.tests.reshape',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.tests.test_expressions',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\test_expressions.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.tests.indexes.numeric.test_numeric',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\numeric\\test_numeric.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_fillna',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_fillna.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_groupby_shift_diff',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\groupby\\test_groupby_shift_diff.py',
   'PYMODULE'),
  ('pandas.tests.plotting.frame',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\plotting\\frame\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_reindex_like',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_reindex_like.py',
   'PYMODULE'),
  ('pandas.tests.series.test_subclass',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\test_subclass.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_to_dict',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_to_dict.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_to_frame',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_to_frame.py',
   'PYMODULE'),
  ('pandas.tests.tseries.holiday.test_federal',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tseries\\holiday\\test_federal.py',
   'PYMODULE'),
  ('pandas.tests.generic.test_label_or_level_utils',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\generic\\test_label_or_level_utils.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_view',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_view.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_repeat',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_repeat.py',
   'PYMODULE'),
  ('pandas.tests.config',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\config\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_dropna',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_dropna.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_reindex',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_reindex.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class.test_where',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\base_class\\test_where.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_array_ops',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arithmetic\\test_array_ops.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_copy',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_copy.py',
   'PYMODULE'),
  ('pandas.tests.indexes.ranges.test_setops',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\ranges\\test_setops.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_rank',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\groupby\\test_rank.py',
   'PYMODULE'),
  ('pandas.tests.arrays.test_datetimes',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\test_datetimes.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.tests.tseries.holiday.test_calendar',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tseries\\holiday\\test_calendar.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_tz_localize',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_tz_localize.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_array_to_datetime',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tslibs\\test_array_to_datetime.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_offsets',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_offsets.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_sort_index',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_sort_index.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_formats',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_formats.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_combine_first',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_combine_first.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_delete',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_delete.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_sample',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\groupby\\test_sample.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.ranges.test_constructors',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\ranges\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_to_csv',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_to_csv.py',
   'PYMODULE'),
  ('pandas.tests.tools.test_to_datetime',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\tools\\test_to_datetime.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_object',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arithmetic\\test_object.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.test_inference',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\dtypes\\test_inference.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_check_indexer',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexing\\test_check_indexer.py',
   'PYMODULE'),
  ('pandas.util._doctools',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\util\\_doctools.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_matmul',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_matmul.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_indexing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_period',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\test_period.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge.test_multi',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\reshape\\merge\\test_multi.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_missing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_missing.py',
   'PYMODULE'),
  ('pandas.tests.extension',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\extension\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_astype',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_ops',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_ops.py',
   'PYMODULE'),
  ('pandas.tests.util.test_validate_kwargs',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\util\\test_validate_kwargs.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\multi\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_indexing',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_indexing.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_equals',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_equals.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.test_rendering',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\test_rendering.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_explode',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_explode.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_datetime',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_datetime.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_drop_duplicates',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\series\\methods\\test_drop_duplicates.py',
   'PYMODULE'),
  ('pandas.core.index',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\core\\index.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_series',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_series.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_function',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_function.py',
   'PYMODULE'),
  ('pandas.tests.indexes.numeric',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\indexes\\numeric\\__init__.py',
   'PYMODULE'),
  ('pandas.tests',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_values',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\frame\\methods\\test_values.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_downcast',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_downcast.py',
   'PYMODULE'),
  ('stringprep', 'D:\\anaconda3\\envs\\dabao\\lib\\stringprep.py', 'PYMODULE'),
  ('_py_abc', 'D:\\anaconda3\\envs\\dabao\\lib\\_py_abc.py', 'PYMODULE'),
  ('platform', 'D:\\anaconda3\\envs\\dabao\\lib\\platform.py', 'PYMODULE'),
  ('_strptime', 'D:\\anaconda3\\envs\\dabao\\lib\\_strptime.py', 'PYMODULE'),
  ('pypinyin',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pypinyin\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\anaconda3\\envs\\dabao\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\anaconda3\\envs\\dabao\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\anaconda3\\envs\\dabao\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\anaconda3\\envs\\dabao\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'D:\\anaconda3\\envs\\dabao\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('json', 'D:\\anaconda3\\envs\\dabao\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder',
   'D:\\anaconda3\\envs\\dabao\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'D:\\anaconda3\\envs\\dabao\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\anaconda3\\envs\\dabao\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('datetime', 'D:\\anaconda3\\envs\\dabao\\lib\\datetime.py', 'PYMODULE'),
  ('threading', 'D:\\anaconda3\\envs\\dabao\\lib\\threading.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\anaconda3\\envs\\dabao\\lib\\_threading_local.py',
   'PYMODULE'),
  ('typing', 'D:\\anaconda3\\envs\\dabao\\lib\\typing.py', 'PYMODULE'),
  ('difflib', 'D:\\anaconda3\\envs\\dabao\\lib\\difflib.py', 'PYMODULE'),
  ('glob', 'D:\\anaconda3\\envs\\dabao\\lib\\glob.py', 'PYMODULE'),
  ('os', 'D:\\anaconda3\\envs\\dabao\\lib\\os.py', 'PYMODULE'),
  ('pandas',
   'D:\\anaconda3\\envs\\dabao\\lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('tkinter.scrolledtext',
   'D:\\anaconda3\\envs\\dabao\\lib\\tkinter\\scrolledtext.py',
   'PYMODULE'),
  ('tkinter.constants',
   'D:\\anaconda3\\envs\\dabao\\lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'D:\\anaconda3\\envs\\dabao\\lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'D:\\anaconda3\\envs\\dabao\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'D:\\anaconda3\\envs\\dabao\\lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'D:\\anaconda3\\envs\\dabao\\lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'D:\\anaconda3\\envs\\dabao\\lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('tkinter',
   'D:\\anaconda3\\envs\\dabao\\lib\\tkinter\\__init__.py',
   'PYMODULE')])
