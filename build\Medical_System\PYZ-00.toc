('E:\\code\\yiyaoxinxichuli\\build\\Medical_System\\PYZ-00.pyz',
 [('PyInstaller',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\PyInstaller\\__init__.py',
   'PYMODULE'),
  ('PyInstaller._shared_with_waf',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\PyInstaller\\_shared_with_waf.py',
   'PYMODULE'),
  ('PyInstaller.building',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\PyInstaller\\building\\__init__.py',
   'PYMODULE'),
  ('PyInstaller.building.utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\PyInstaller\\building\\utils.py',
   'PYMODULE'),
  ('PyInstaller.compat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\PyInstaller\\compat.py',
   'PYMODULE'),
  ('PyInstaller.config',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\PyInstaller\\config.py',
   'PYMODULE'),
  ('PyInstaller.depend',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\PyInstaller\\depend\\__init__.py',
   'PYMODULE'),
  ('PyInstaller.depend.imphookapi',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\PyInstaller\\depend\\imphookapi.py',
   'PYMODULE'),
  ('PyInstaller.exceptions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\PyInstaller\\exceptions.py',
   'PYMODULE'),
  ('PyInstaller.isolated',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\PyInstaller\\isolated\\__init__.py',
   'PYMODULE'),
  ('PyInstaller.isolated._parent',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\PyInstaller\\isolated\\_parent.py',
   'PYMODULE'),
  ('PyInstaller.lib',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\PyInstaller\\lib\\__init__.py',
   'PYMODULE'),
  ('PyInstaller.lib.modulegraph',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\PyInstaller\\lib\\modulegraph\\__init__.py',
   'PYMODULE'),
  ('PyInstaller.lib.modulegraph.modulegraph',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\PyInstaller\\lib\\modulegraph\\modulegraph.py',
   'PYMODULE'),
  ('PyInstaller.lib.modulegraph.util',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\PyInstaller\\lib\\modulegraph\\util.py',
   'PYMODULE'),
  ('PyInstaller.log',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\PyInstaller\\log.py',
   'PYMODULE'),
  ('PyInstaller.utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\PyInstaller\\utils\\__init__.py',
   'PYMODULE'),
  ('PyInstaller.utils.hooks',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\PyInstaller\\utils\\hooks\\__init__.py',
   'PYMODULE'),
  ('PyInstaller.utils.hooks.conda',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\PyInstaller\\utils\\hooks\\conda.py',
   'PYMODULE'),
  ('PyInstaller.utils.misc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\PyInstaller\\utils\\misc.py',
   'PYMODULE'),
  ('PyInstaller.utils.osx',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\PyInstaller\\utils\\osx.py',
   'PYMODULE'),
  ('PyInstaller.utils.win32',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\PyInstaller\\utils\\win32\\__init__.py',
   'PYMODULE'),
  ('PyInstaller.utils.win32.versioninfo',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\PyInstaller\\utils\\win32\\versioninfo.py',
   'PYMODULE'),
  ('__future__',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_colorize',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_colorize.py',
   'PYMODULE'),
  ('_compat_pickle',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_ios_support',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_ios_support.py',
   'PYMODULE'),
  ('_opcode_metadata',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_opcode_metadata.py',
   'PYMODULE'),
  ('_py_abc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydatetime',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_pyrepl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\__init__.py',
   'PYMODULE'),
  ('_pyrepl._minimal_curses',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\_minimal_curses.py',
   'PYMODULE'),
  ('_pyrepl._threading_handler',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\_threading_handler.py',
   'PYMODULE'),
  ('_pyrepl.base_eventqueue',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\base_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.commands',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\commands.py',
   'PYMODULE'),
  ('_pyrepl.completing_reader',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\completing_reader.py',
   'PYMODULE'),
  ('_pyrepl.console',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\console.py',
   'PYMODULE'),
  ('_pyrepl.curses',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\curses.py',
   'PYMODULE'),
  ('_pyrepl.fancy_termios',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\fancy_termios.py',
   'PYMODULE'),
  ('_pyrepl.historical_reader',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\historical_reader.py',
   'PYMODULE'),
  ('_pyrepl.input',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\input.py',
   'PYMODULE'),
  ('_pyrepl.keymap',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\keymap.py',
   'PYMODULE'),
  ('_pyrepl.main',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\main.py',
   'PYMODULE'),
  ('_pyrepl.pager',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\pager.py',
   'PYMODULE'),
  ('_pyrepl.reader',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\reader.py',
   'PYMODULE'),
  ('_pyrepl.readline',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\readline.py',
   'PYMODULE'),
  ('_pyrepl.simple_interact',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\simple_interact.py',
   'PYMODULE'),
  ('_pyrepl.trace',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\trace.py',
   'PYMODULE'),
  ('_pyrepl.types',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\types.py',
   'PYMODULE'),
  ('_pyrepl.unix_console',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\unix_console.py',
   'PYMODULE'),
  ('_pyrepl.unix_eventqueue',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\unix_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\utils.py',
   'PYMODULE'),
  ('_pyrepl.windows_console',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\windows_console.py',
   'PYMODULE'),
  ('_pyrepl.windows_eventqueue',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_pyrepl\\windows_eventqueue.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('altgraph',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\altgraph\\__init__.py',
   'PYMODULE'),
  ('altgraph.Graph',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\altgraph\\Graph.py',
   'PYMODULE'),
  ('altgraph.GraphUtil',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\altgraph\\GraphUtil.py',
   'PYMODULE'),
  ('altgraph.ObjectGraph',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\altgraph\\ObjectGraph.py',
   'PYMODULE'),
  ('argparse',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\argparse.py',
   'PYMODULE'),
  ('ast', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\ast.py', 'PYMODULE'),
  ('backports',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\base64.py', 'PYMODULE'),
  ('bdb', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\calendar.py',
   'PYMODULE'),
  ('cmd', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\codeop.py', 'PYMODULE'),
  ('concurrent',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('curses',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\curses\\__init__.py',
   'PYMODULE'),
  ('curses.has_key',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\curses\\has_key.py',
   'PYMODULE'),
  ('dataclasses',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\datetime.py',
   'PYMODULE'),
  ('dateutil',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('decimal',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\decimal.py',
   'PYMODULE'),
  ('difflib',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\difflib.py',
   'PYMODULE'),
  ('dis', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\dis.py', 'PYMODULE'),
  ('doctest',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\doctest.py',
   'PYMODULE'),
  ('email',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('et_xmlfile',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('fileinput',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\fileinput.py',
   'PYMODULE'),
  ('fnmatch',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('fractions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\fractions.py',
   'PYMODULE'),
  ('ftplib', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\gettext.py',
   'PYMODULE'),
  ('glob', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\glob.py', 'PYMODULE'),
  ('gzip', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\hashlib.py',
   'PYMODULE'),
  ('html',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('http',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.server',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\http\\server.py',
   'PYMODULE'),
  ('importlib',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('json',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\lzma.py', 'PYMODULE'),
  ('mimetypes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('mkl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\mkl\\__init__.py',
   'PYMODULE'),
  ('netrc', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\numbers.py',
   'PYMODULE'),
  ('numexpr',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numexpr\\__init__.py',
   'PYMODULE'),
  ('numexpr.cpuinfo',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numexpr\\cpuinfo.py',
   'PYMODULE'),
  ('numexpr.expressions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numexpr\\expressions.py',
   'PYMODULE'),
  ('numexpr.necompiler',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numexpr\\necompiler.py',
   'PYMODULE'),
  ('numexpr.tests',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numexpr\\tests\\__init__.py',
   'PYMODULE'),
  ('numexpr.tests.test_numexpr',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numexpr\\tests\\test_numexpr.py',
   'PYMODULE'),
  ('numexpr.utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numexpr\\utils.py',
   'PYMODULE'),
  ('numexpr.version',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numexpr\\version.py',
   'PYMODULE'),
  ('numpy',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy._core',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.records',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\ctypeslib\\__init__.py',
   'PYMODULE'),
  ('numpy.ctypeslib._ctypeslib',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\ctypeslib\\_ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._format_impl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_format_impl.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.typing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\opcode.py', 'PYMODULE'),
  ('openpyxl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.abc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\compat\\abc.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.product',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\compat\\product.py',
   'PYMODULE'),
  ('openpyxl.compat.singleton',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\compat\\singleton.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.descriptors.slots',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\descriptors\\slots.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.interface',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\packaging\\interface.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.dataframe',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\utils\\dataframe.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.inference',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\utils\\inference.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_watch',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\cell_watch.py',
   'PYMODULE'),
  ('openpyxl.worksheet.controls',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\controls.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.custom',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.errors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\errors.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.ole',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\ole.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.picture',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\picture.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.smart_tag',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\smart_tag.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('ordlookup',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\ordlookup\\__init__.py',
   'PYMODULE'),
  ('ordlookup.oleaut32',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\ordlookup\\oleaut32.py',
   'PYMODULE'),
  ('ordlookup.ws2_32',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\ordlookup\\ws2_32.py',
   'PYMODULE'),
  ('packaging',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pandas',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._config',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._config.display',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas._libs',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._testing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing._hypothesis',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\_testing\\_hypothesis.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._typing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas._version',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE'),
  ('pandas.api',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.api.types',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.conftest',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\conftest.py',
   'PYMODULE'),
  ('pandas.core',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba.extensions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.api',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays._utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.accessors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.base',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.series',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.sparse',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.sparse.api',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\sparse\\api.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.util',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.errors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.io',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.io._util',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.io.api',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._calamine',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.io.html',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.io.json',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.plotting',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.boxplot',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\boxplot.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.converter',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\converter.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.core',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\core.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.groupby',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\groupby.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.hist',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\hist.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.misc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\misc.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.style',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\style.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.timeseries',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\timeseries.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.tools',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\tools.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.testing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas.tests',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.api',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.api.test_api',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\api\\test_api.py',
   'PYMODULE'),
  ('pandas.tests.api.test_types',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\api\\test_types.py',
   'PYMODULE'),
  ('pandas.tests.apply',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\apply\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.apply.common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\apply\\common.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_frame_apply',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\apply\\test_frame_apply.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_frame_apply_relabeling',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\apply\\test_frame_apply_relabeling.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_frame_transform',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\apply\\test_frame_transform.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_invalid_arg',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\apply\\test_invalid_arg.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_numba',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\apply\\test_numba.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_series_apply',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\apply\\test_series_apply.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_series_apply_relabeling',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\apply\\test_series_apply_relabeling.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_series_transform',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\apply\\test_series_transform.py',
   'PYMODULE'),
  ('pandas.tests.apply.test_str',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\apply\\test_str.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arithmetic\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arithmetic\\common.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.conftest',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arithmetic\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_array_ops',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arithmetic\\test_array_ops.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_categorical',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arithmetic\\test_categorical.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_datetime64',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arithmetic\\test_datetime64.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_interval',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arithmetic\\test_interval.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_numeric',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arithmetic\\test_numeric.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_object',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arithmetic\\test_object.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_period',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arithmetic\\test_period.py',
   'PYMODULE'),
  ('pandas.tests.arithmetic.test_timedelta64',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arithmetic\\test_timedelta64.py',
   'PYMODULE'),
  ('pandas.tests.arrays',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_arithmetic',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_astype',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_comparison',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_comparison.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_construction',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_construction.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_function',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_function.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_indexing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_logical',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_logical.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_ops',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_ops.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_reduction',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_reduction.py',
   'PYMODULE'),
  ('pandas.tests.arrays.boolean.test_repr',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_repr.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_algos',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_algos.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_analytics',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_analytics.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_api',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_api.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_astype',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_constructors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_dtypes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_indexing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_map',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_map.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_missing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_missing.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_operators',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_operators.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_replace',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_replace.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_repr',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_repr.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_sorting',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_sorting.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_subclass',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_subclass.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_take',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_take.py',
   'PYMODULE'),
  ('pandas.tests.arrays.categorical.test_warnings',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_warnings.py',
   'PYMODULE'),
  ('pandas.tests.arrays.datetimes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\datetimes\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.datetimes.test_constructors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\datetimes\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.arrays.datetimes.test_cumulative',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\datetimes\\test_cumulative.py',
   'PYMODULE'),
  ('pandas.tests.arrays.datetimes.test_reductions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\datetimes\\test_reductions.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.conftest',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_arithmetic',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_astype',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_comparison',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_comparison.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_concat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_concat.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_construction',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_construction.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_contains',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_contains.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_function',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_function.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_repr',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_repr.py',
   'PYMODULE'),
  ('pandas.tests.arrays.floating.test_to_numpy',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_to_numpy.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.conftest',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_arithmetic',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_comparison',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_comparison.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_concat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_concat.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_construction',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_construction.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_dtypes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_function',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_function.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_indexing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_reduction',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_reduction.py',
   'PYMODULE'),
  ('pandas.tests.arrays.integer.test_repr',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_repr.py',
   'PYMODULE'),
  ('pandas.tests.arrays.interval',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\interval\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.interval.test_astype',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\interval\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.arrays.interval.test_formats',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\interval\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.arrays.interval.test_interval',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\interval\\test_interval.py',
   'PYMODULE'),
  ('pandas.tests.arrays.interval.test_interval_pyarrow',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\interval\\test_interval_pyarrow.py',
   'PYMODULE'),
  ('pandas.tests.arrays.interval.test_overlaps',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\interval\\test_overlaps.py',
   'PYMODULE'),
  ('pandas.tests.arrays.masked',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\masked\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.masked.test_arithmetic',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\masked\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.arrays.masked.test_arrow_compat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\masked\\test_arrow_compat.py',
   'PYMODULE'),
  ('pandas.tests.arrays.masked.test_function',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\masked\\test_function.py',
   'PYMODULE'),
  ('pandas.tests.arrays.masked.test_indexing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\masked\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.arrays.masked_shared',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\masked_shared.py',
   'PYMODULE'),
  ('pandas.tests.arrays.numpy_',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\numpy_\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.numpy_.test_indexing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\numpy_\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.arrays.numpy_.test_numpy',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\numpy_\\test_numpy.py',
   'PYMODULE'),
  ('pandas.tests.arrays.period',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\period\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.period.test_arrow_compat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\period\\test_arrow_compat.py',
   'PYMODULE'),
  ('pandas.tests.arrays.period.test_astype',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\period\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.arrays.period.test_constructors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\period\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.arrays.period.test_reductions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\period\\test_reductions.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_accessor',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_accessor.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_arithmetics',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_arithmetics.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_array',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_array.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_astype',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_combine_concat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_combine_concat.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_constructors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_dtype',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_dtype.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_indexing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_libsparse',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_libsparse.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_reductions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_reductions.py',
   'PYMODULE'),
  ('pandas.tests.arrays.sparse.test_unary',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_unary.py',
   'PYMODULE'),
  ('pandas.tests.arrays.string_',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\string_\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.string_.test_string',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\string_\\test_string.py',
   'PYMODULE'),
  ('pandas.tests.arrays.string_.test_string_arrow',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\string_\\test_string_arrow.py',
   'PYMODULE'),
  ('pandas.tests.arrays.test_array',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\test_array.py',
   'PYMODULE'),
  ('pandas.tests.arrays.test_datetimelike',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\test_datetimelike.py',
   'PYMODULE'),
  ('pandas.tests.arrays.test_datetimes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\test_datetimes.py',
   'PYMODULE'),
  ('pandas.tests.arrays.test_ndarray_backed',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\test_ndarray_backed.py',
   'PYMODULE'),
  ('pandas.tests.arrays.test_period',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\test_period.py',
   'PYMODULE'),
  ('pandas.tests.arrays.test_timedeltas',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\test_timedeltas.py',
   'PYMODULE'),
  ('pandas.tests.arrays.timedeltas',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\timedeltas\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.arrays.timedeltas.test_constructors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\timedeltas\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.arrays.timedeltas.test_cumulative',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\timedeltas\\test_cumulative.py',
   'PYMODULE'),
  ('pandas.tests.arrays.timedeltas.test_reductions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\arrays\\timedeltas\\test_reductions.py',
   'PYMODULE'),
  ('pandas.tests.base',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\base\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.base.common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\base\\common.py',
   'PYMODULE'),
  ('pandas.tests.base.test_constructors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\base\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.base.test_conversion',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\base\\test_conversion.py',
   'PYMODULE'),
  ('pandas.tests.base.test_fillna',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\base\\test_fillna.py',
   'PYMODULE'),
  ('pandas.tests.base.test_misc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\base\\test_misc.py',
   'PYMODULE'),
  ('pandas.tests.base.test_transpose',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\base\\test_transpose.py',
   'PYMODULE'),
  ('pandas.tests.base.test_unique',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\base\\test_unique.py',
   'PYMODULE'),
  ('pandas.tests.base.test_value_counts',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\base\\test_value_counts.py',
   'PYMODULE'),
  ('pandas.tests.computation',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.computation.test_compat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\computation\\test_compat.py',
   'PYMODULE'),
  ('pandas.tests.computation.test_eval',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\computation\\test_eval.py',
   'PYMODULE'),
  ('pandas.tests.config',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\config\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.config.test_config',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\config\\test_config.py',
   'PYMODULE'),
  ('pandas.tests.config.test_localization',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\config\\test_localization.py',
   'PYMODULE'),
  ('pandas.tests.construction',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\construction\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.construction.test_extract_array',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\construction\\test_extract_array.py',
   'PYMODULE'),
  ('pandas.tests.copy_view',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\copy_view\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.index',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\copy_view\\index\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.index.test_datetimeindex',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\copy_view\\index\\test_datetimeindex.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.index.test_index',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\copy_view\\index\\test_index.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.index.test_periodindex',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\copy_view\\index\\test_periodindex.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.index.test_timedeltaindex',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\copy_view\\index\\test_timedeltaindex.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_array',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_array.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_astype',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_chained_assignment_deprecation',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_chained_assignment_deprecation.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_clip',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_clip.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_constructors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_core_functionalities',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_core_functionalities.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_functions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_functions.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_indexing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_internals',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_internals.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_interp_fillna',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_interp_fillna.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_methods',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_methods.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_replace',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_replace.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_setitem',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_setitem.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.test_util',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_util.py',
   'PYMODULE'),
  ('pandas.tests.copy_view.util',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\copy_view\\util.py',
   'PYMODULE'),
  ('pandas.tests.dtypes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_can_hold_element',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_can_hold_element.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_construct_from_scalar',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_construct_from_scalar.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_construct_ndarray',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_construct_ndarray.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_construct_object_arr',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_construct_object_arr.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_dict_compat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_dict_compat.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_downcast',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_downcast.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_find_common_type',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_find_common_type.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_infer_datetimelike',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_infer_datetimelike.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_infer_dtype',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_infer_dtype.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_maybe_box_native',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_maybe_box_native.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.cast.test_promote',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_promote.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.test_common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\dtypes\\test_common.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.test_concat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\dtypes\\test_concat.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.test_dtypes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\dtypes\\test_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.test_generic',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\dtypes\\test_generic.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.test_inference',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\dtypes\\test_inference.py',
   'PYMODULE'),
  ('pandas.tests.dtypes.test_missing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\dtypes\\test_missing.py',
   'PYMODULE'),
  ('pandas.tests.extension',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.extension.array_with_attr',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\array_with_attr\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.extension.array_with_attr.array',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\array_with_attr\\array.py',
   'PYMODULE'),
  ('pandas.tests.extension.array_with_attr.test_array_with_attr',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\array_with_attr\\test_array_with_attr.py',
   'PYMODULE'),
  ('pandas.tests.extension.base',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\base\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.accumulate',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\base\\accumulate.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.casting',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\base\\casting.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.constructors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\base\\constructors.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.dim2',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\base\\dim2.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.dtype',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\base\\dtype.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.getitem',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\base\\getitem.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.groupby',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\base\\groupby.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.index',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\base\\index.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.interface',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\base\\interface.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.io',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\base\\io.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.methods',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\base\\methods.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.missing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\base\\missing.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.ops',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\base\\ops.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.printing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\base\\printing.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.reduce',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\base\\reduce.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.reshaping',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\base\\reshaping.py',
   'PYMODULE'),
  ('pandas.tests.extension.base.setitem',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\base\\setitem.py',
   'PYMODULE'),
  ('pandas.tests.extension.conftest',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.extension.date',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\date\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.extension.date.array',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\date\\array.py',
   'PYMODULE'),
  ('pandas.tests.extension.decimal',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\decimal\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.extension.decimal.array',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\decimal\\array.py',
   'PYMODULE'),
  ('pandas.tests.extension.decimal.test_decimal',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\decimal\\test_decimal.py',
   'PYMODULE'),
  ('pandas.tests.extension.json',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.extension.json.array',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\json\\array.py',
   'PYMODULE'),
  ('pandas.tests.extension.json.test_json',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\json\\test_json.py',
   'PYMODULE'),
  ('pandas.tests.extension.list',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\list\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.extension.list.array',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\list\\array.py',
   'PYMODULE'),
  ('pandas.tests.extension.list.test_list',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\list\\test_list.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_arrow',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\test_arrow.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_categorical',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\test_categorical.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\test_common.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_datetime',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\test_datetime.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_extension',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\test_extension.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_interval',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\test_interval.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_masked',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\test_masked.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_numpy',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\test_numpy.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_period',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\test_period.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_sparse',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\test_sparse.py',
   'PYMODULE'),
  ('pandas.tests.extension.test_string',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\extension\\test_string.py',
   'PYMODULE'),
  ('pandas.tests.frame',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.frame.common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\common.py',
   'PYMODULE'),
  ('pandas.tests.frame.conftest',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.frame.constructors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\constructors\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.frame.constructors.test_from_dict',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\constructors\\test_from_dict.py',
   'PYMODULE'),
  ('pandas.tests.frame.constructors.test_from_records',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\constructors\\test_from_records.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_coercion',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_coercion.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_delitem',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_delitem.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_get',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_get.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_get_value',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_get_value.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_getitem',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_getitem.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_indexing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_insert',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_insert.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_mask',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_mask.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_set_value',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_set_value.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_setitem',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_setitem.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_take',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_take.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_where',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_where.py',
   'PYMODULE'),
  ('pandas.tests.frame.indexing.test_xs',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_xs.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_add_prefix_suffix',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_add_prefix_suffix.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_align',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_align.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_asfreq',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_asfreq.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_asof',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_asof.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_assign',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_assign.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_astype',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_at_time',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_at_time.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_between_time',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_between_time.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_clip',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_clip.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_combine',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_combine.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_combine_first',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_combine_first.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_compare',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_compare.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_convert_dtypes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_convert_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_copy',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_copy.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_count',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_count.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_cov_corr',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_cov_corr.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_describe',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_describe.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_diff',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_diff.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_dot',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_dot.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_drop',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_drop.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_drop_duplicates',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_drop_duplicates.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_droplevel',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_droplevel.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_dropna',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_dropna.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_dtypes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_duplicated',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_duplicated.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_equals',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_equals.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_explode',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_explode.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_fillna',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_fillna.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_filter',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_filter.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_first_and_last',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_first_and_last.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_first_valid_index',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_first_valid_index.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_get_numeric_data',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_get_numeric_data.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_head_tail',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_head_tail.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_infer_objects',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_infer_objects.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_info',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_info.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_interpolate',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_interpolate.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_is_homogeneous_dtype',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_is_homogeneous_dtype.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_isetitem',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_isetitem.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_isin',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_isin.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_iterrows',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_iterrows.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_join',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_map',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_map.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_matmul',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_matmul.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_nlargest',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_nlargest.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_pct_change',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_pct_change.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_pipe',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_pipe.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_pop',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_pop.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_quantile',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_quantile.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_rank',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_rank.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_reindex',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_reindex.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_reindex_like',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_reindex_like.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_rename',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_rename.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_rename_axis',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_rename_axis.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_reorder_levels',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_reorder_levels.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_replace',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_replace.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_reset_index',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_reset_index.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_round',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_round.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_sample',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_sample.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_select_dtypes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_select_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_set_axis',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_set_axis.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_set_index',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_set_index.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_shift',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_shift.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_size',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_size.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_sort_index',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_sort_index.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_sort_values',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_sort_values.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_swapaxes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_swapaxes.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_swaplevel',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_swaplevel.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_to_csv',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_to_csv.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_to_dict',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_to_dict.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_to_dict_of_blocks',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_to_dict_of_blocks.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_to_numpy',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_to_numpy.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_to_period',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_to_period.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_to_records',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_to_records.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_to_timestamp',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_to_timestamp.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_transpose',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_transpose.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_truncate',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_truncate.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_tz_convert',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_tz_convert.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_tz_localize',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_tz_localize.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_update',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_update.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_value_counts',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_value_counts.py',
   'PYMODULE'),
  ('pandas.tests.frame.methods.test_values',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_values.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_alter_axes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\test_alter_axes.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_api',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\test_api.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_arithmetic',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_arrow_interface',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\test_arrow_interface.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_block_internals',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\test_block_internals.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_constructors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_cumulative',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\test_cumulative.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_iteration',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\test_iteration.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_logical_ops',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\test_logical_ops.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_nonunique_indexes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\test_nonunique_indexes.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_npfuncs',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\test_npfuncs.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_query_eval',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\test_query_eval.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_reductions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\test_reductions.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_repr',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\test_repr.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_stack_unstack',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\test_stack_unstack.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_subclass',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\test_subclass.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_ufunc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\test_ufunc.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_unary',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\test_unary.py',
   'PYMODULE'),
  ('pandas.tests.frame.test_validate',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\frame\\test_validate.py',
   'PYMODULE'),
  ('pandas.tests.generic',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\generic\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.generic.test_duplicate_labels',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\generic\\test_duplicate_labels.py',
   'PYMODULE'),
  ('pandas.tests.generic.test_finalize',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\generic\\test_finalize.py',
   'PYMODULE'),
  ('pandas.tests.generic.test_frame',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\generic\\test_frame.py',
   'PYMODULE'),
  ('pandas.tests.generic.test_generic',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\generic\\test_generic.py',
   'PYMODULE'),
  ('pandas.tests.generic.test_label_or_level_utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\generic\\test_label_or_level_utils.py',
   'PYMODULE'),
  ('pandas.tests.generic.test_series',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\generic\\test_series.py',
   'PYMODULE'),
  ('pandas.tests.generic.test_to_xarray',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\generic\\test_to_xarray.py',
   'PYMODULE'),
  ('pandas.tests.groupby',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.groupby.aggregate',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\aggregate\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.groupby.aggregate.test_aggregate',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\aggregate\\test_aggregate.py',
   'PYMODULE'),
  ('pandas.tests.groupby.aggregate.test_cython',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\aggregate\\test_cython.py',
   'PYMODULE'),
  ('pandas.tests.groupby.aggregate.test_numba',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\aggregate\\test_numba.py',
   'PYMODULE'),
  ('pandas.tests.groupby.aggregate.test_other',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\aggregate\\test_other.py',
   'PYMODULE'),
  ('pandas.tests.groupby.conftest',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_corrwith',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_corrwith.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_describe',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_describe.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_groupby_shift_diff',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_groupby_shift_diff.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_is_monotonic',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_is_monotonic.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_nlargest_nsmallest',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_nlargest_nsmallest.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_nth',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_nth.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_quantile',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_quantile.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_rank',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_rank.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_sample',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_sample.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_size',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_size.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_skew',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_skew.py',
   'PYMODULE'),
  ('pandas.tests.groupby.methods.test_value_counts',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_value_counts.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_all_methods',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\test_all_methods.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_api',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\test_api.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_apply',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\test_apply.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_apply_mutate',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\test_apply_mutate.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_bin_groupby',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\test_bin_groupby.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_categorical',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\test_categorical.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_counting',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\test_counting.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_cumulative',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\test_cumulative.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_filters',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\test_filters.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_groupby',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\test_groupby.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_groupby_dropna',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\test_groupby_dropna.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_groupby_subclass',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\test_groupby_subclass.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_grouping',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\test_grouping.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_index_as_string',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\test_index_as_string.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_indexing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_libgroupby',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\test_libgroupby.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_missing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\test_missing.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_numba',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\test_numba.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_numeric_only',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\test_numeric_only.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_pipe',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\test_pipe.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_raises',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\test_raises.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_reductions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\test_reductions.py',
   'PYMODULE'),
  ('pandas.tests.groupby.test_timegrouper',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\test_timegrouper.py',
   'PYMODULE'),
  ('pandas.tests.groupby.transform',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\transform\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.groupby.transform.test_numba',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\transform\\test_numba.py',
   'PYMODULE'),
  ('pandas.tests.groupby.transform.test_transform',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\groupby\\transform\\test_transform.py',
   'PYMODULE'),
  ('pandas.tests.indexes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\base_class\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class.test_constructors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\base_class\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class.test_formats',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\base_class\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class.test_indexing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\base_class\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class.test_pickle',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\base_class\\test_pickle.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class.test_reshape',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\base_class\\test_reshape.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class.test_setops',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\base_class\\test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.base_class.test_where',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\base_class\\test_where.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_append',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_append.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_astype',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_category',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_category.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_constructors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_equals',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_equals.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_fillna',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_fillna.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_formats',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_indexing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_map',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_map.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_reindex',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_reindex.py',
   'PYMODULE'),
  ('pandas.tests.indexes.categorical.test_setops',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.conftest',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_.test_drop_duplicates',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\test_drop_duplicates.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_.test_equals',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\test_equals.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_.test_indexing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_.test_is_monotonic',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\test_is_monotonic.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_.test_nat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\test_nat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_.test_sort_values',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\test_sort_values.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimelike_.test_value_counts',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\test_value_counts.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_asof',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_asof.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_astype',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_delete',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_delete.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_factorize',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_factorize.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_fillna',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_fillna.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_insert',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_insert.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_isocalendar',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_isocalendar.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_map',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_map.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_normalize',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_normalize.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_repeat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_repeat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_resolution',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_resolution.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_round',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_round.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_shift',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_shift.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_snap',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_snap.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_to_frame',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_to_frame.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_to_julian_date',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_to_julian_date.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_to_period',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_to_period.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_to_pydatetime',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_to_pydatetime.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_to_series',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_to_series.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_tz_convert',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_tz_convert.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_tz_localize',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_tz_localize.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.methods.test_unique',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_unique.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_arithmetic',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_constructors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_date_range',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_date_range.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_datetime',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_datetime.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_formats',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_freq_attr',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_freq_attr.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_indexing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_iter',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_iter.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_join',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_npfuncs',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_npfuncs.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_ops',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_ops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_partial_slicing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_partial_slicing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_pickle',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_pickle.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_reindex',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_reindex.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_scalar_compat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_scalar_compat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_setops',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.datetimes.test_timezones',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_timezones.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_astype',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_constructors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_equals',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_equals.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_formats',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_indexing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_interval',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_interval.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_interval_range',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_interval_range.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_interval_tree',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_interval_tree.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_join',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_pickle',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_pickle.py',
   'PYMODULE'),
  ('pandas.tests.indexes.interval.test_setops',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.conftest',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_analytics',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_analytics.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_astype',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_compat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_compat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_constructors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_conversion',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_conversion.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_copy',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_copy.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_drop',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_drop.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_duplicates',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_duplicates.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_equivalence',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_equivalence.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_formats',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_get_level_values',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_get_level_values.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_get_set',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_get_set.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_indexing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_integrity',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_integrity.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_isin',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_isin.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_join',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_lexsort',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_lexsort.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_missing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_missing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_monotonic',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_monotonic.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_names',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_names.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_partial_indexing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_partial_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_pickle',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_pickle.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_reindex',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_reindex.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_reshape',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_reshape.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_setops',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_sorting',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_sorting.py',
   'PYMODULE'),
  ('pandas.tests.indexes.multi.test_take',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_take.py',
   'PYMODULE'),
  ('pandas.tests.indexes.numeric',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\numeric\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.numeric.test_astype',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\numeric\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.numeric.test_indexing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\numeric\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.numeric.test_join',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\numeric\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.indexes.numeric.test_numeric',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\numeric\\test_numeric.py',
   'PYMODULE'),
  ('pandas.tests.indexes.numeric.test_setops',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\numeric\\test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.object',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\object\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.object.test_astype',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\object\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.object.test_indexing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\object\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_asfreq',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_asfreq.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_astype',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_factorize',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_factorize.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_fillna',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_fillna.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_insert',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_insert.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_is_full',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_is_full.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_repeat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_repeat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_shift',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_shift.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.methods.test_to_timestamp',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_to_timestamp.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_constructors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_formats',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_freq_attr',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_freq_attr.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_indexing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_join',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_monotonic',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_monotonic.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_partial_slicing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_partial_slicing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_period',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_period.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_period_range',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_period_range.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_pickle',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_pickle.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_resolution',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_resolution.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_scalar_compat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_scalar_compat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_searchsorted',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_searchsorted.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_setops',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.period.test_tools',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_tools.py',
   'PYMODULE'),
  ('pandas.tests.indexes.ranges',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\ranges\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.ranges.test_constructors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\ranges\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.indexes.ranges.test_indexing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\ranges\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.ranges.test_join',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\ranges\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.indexes.ranges.test_range',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\ranges\\test_range.py',
   'PYMODULE'),
  ('pandas.tests.indexes.ranges.test_setops',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\ranges\\test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_any_index',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\test_any_index.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_base',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\test_base.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\test_common.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_datetimelike',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\test_datetimelike.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_engines',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\test_engines.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_frozen',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\test_frozen.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_index_new',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\test_index_new.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_indexing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_numpy_compat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\test_numpy_compat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_old_base',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\test_old_base.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_setops',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.test_subclass',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\test_subclass.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.methods',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.methods.test_astype',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\methods\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.methods.test_factorize',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\methods\\test_factorize.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.methods.test_fillna',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\methods\\test_fillna.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.methods.test_insert',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\methods\\test_insert.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.methods.test_repeat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\methods\\test_repeat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.methods.test_shift',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\methods\\test_shift.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_arithmetic',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_constructors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_delete',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_delete.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_formats',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_freq_attr',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_freq_attr.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_indexing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_join',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_ops',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_ops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_pickle',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_pickle.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_scalar_compat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_scalar_compat.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_searchsorted',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_searchsorted.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_setops',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_setops.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_timedelta',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_timedelta.py',
   'PYMODULE'),
  ('pandas.tests.indexes.timedeltas.test_timedelta_range',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_timedelta_range.py',
   'PYMODULE'),
  ('pandas.tests.indexing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexing\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexing.common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexing\\common.py',
   'PYMODULE'),
  ('pandas.tests.indexing.conftest',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexing\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.indexing.interval',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexing\\interval\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexing.interval.test_interval',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexing\\interval\\test_interval.py',
   'PYMODULE'),
  ('pandas.tests.indexing.interval.test_interval_new',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexing\\interval\\test_interval_new.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_chaining_and_caching',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_chaining_and_caching.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_datetime',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_datetime.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_getitem',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_getitem.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_iloc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_iloc.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_indexing_slow',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_indexing_slow.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_loc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_loc.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_multiindex',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_multiindex.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_partial',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_partial.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_setitem',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_setitem.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_slice',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_slice.py',
   'PYMODULE'),
  ('pandas.tests.indexing.multiindex.test_sorted',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_sorted.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_at',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexing\\test_at.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_categorical',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexing\\test_categorical.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_chaining_and_caching',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexing\\test_chaining_and_caching.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_check_indexer',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexing\\test_check_indexer.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_coercion',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexing\\test_coercion.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_datetime',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexing\\test_datetime.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_floats',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexing\\test_floats.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_iat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexing\\test_iat.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_iloc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexing\\test_iloc.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_indexers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexing\\test_indexers.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_indexing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexing\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_loc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexing\\test_loc.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_na_indexing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexing\\test_na_indexing.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_partial',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexing\\test_partial.py',
   'PYMODULE'),
  ('pandas.tests.indexing.test_scalar',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\indexing\\test_scalar.py',
   'PYMODULE'),
  ('pandas.tests.interchange',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.interchange.test_impl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\interchange\\test_impl.py',
   'PYMODULE'),
  ('pandas.tests.interchange.test_spec_conformance',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\interchange\\test_spec_conformance.py',
   'PYMODULE'),
  ('pandas.tests.interchange.test_utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\interchange\\test_utils.py',
   'PYMODULE'),
  ('pandas.tests.internals',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.internals.test_api',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\internals\\test_api.py',
   'PYMODULE'),
  ('pandas.tests.internals.test_internals',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\internals\\test_internals.py',
   'PYMODULE'),
  ('pandas.tests.internals.test_managers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\internals\\test_managers.py',
   'PYMODULE'),
  ('pandas.tests.io',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.conftest',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.io.excel',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.excel.test_odf',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\excel\\test_odf.py',
   'PYMODULE'),
  ('pandas.tests.io.excel.test_odswriter',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\excel\\test_odswriter.py',
   'PYMODULE'),
  ('pandas.tests.io.excel.test_openpyxl',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\excel\\test_openpyxl.py',
   'PYMODULE'),
  ('pandas.tests.io.excel.test_readers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\excel\\test_readers.py',
   'PYMODULE'),
  ('pandas.tests.io.excel.test_style',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\excel\\test_style.py',
   'PYMODULE'),
  ('pandas.tests.io.excel.test_writers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\excel\\test_writers.py',
   'PYMODULE'),
  ('pandas.tests.io.excel.test_xlrd',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\excel\\test_xlrd.py',
   'PYMODULE'),
  ('pandas.tests.io.excel.test_xlsxwriter',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\excel\\test_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.tests.io.formats',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_bar',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_bar.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_exceptions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_exceptions.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_format',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_format.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_highlight',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_highlight.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_html',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_html.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_matplotlib',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_matplotlib.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_non_unique',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_non_unique.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_style',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_style.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_to_latex',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_to_latex.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_to_string',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_to_string.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.style.test_tooltip',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_tooltip.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_console',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_console.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_css',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_css.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_eng_formatting',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_eng_formatting.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_format',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_format.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_ipython_compat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_ipython_compat.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_printing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_printing.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_to_csv',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_to_csv.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_to_excel',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_to_excel.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_to_html',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_to_html.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_to_latex',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_to_latex.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_to_markdown',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_to_markdown.py',
   'PYMODULE'),
  ('pandas.tests.io.formats.test_to_string',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_to_string.py',
   'PYMODULE'),
  ('pandas.tests.io.generate_legacy_storage_files',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\generate_legacy_storage_files.py',
   'PYMODULE'),
  ('pandas.tests.io.json',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.json.conftest',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\json\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.io.json.test_compression',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\json\\test_compression.py',
   'PYMODULE'),
  ('pandas.tests.io.json.test_deprecated_kwargs',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\json\\test_deprecated_kwargs.py',
   'PYMODULE'),
  ('pandas.tests.io.json.test_json_table_schema',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\json\\test_json_table_schema.py',
   'PYMODULE'),
  ('pandas.tests.io.json.test_json_table_schema_ext_dtype',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\json\\test_json_table_schema_ext_dtype.py',
   'PYMODULE'),
  ('pandas.tests.io.json.test_normalize',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\json\\test_normalize.py',
   'PYMODULE'),
  ('pandas.tests.io.json.test_pandas',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\json\\test_pandas.py',
   'PYMODULE'),
  ('pandas.tests.io.json.test_readlines',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\json\\test_readlines.py',
   'PYMODULE'),
  ('pandas.tests.io.json.test_ujson',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\json\\test_ujson.py',
   'PYMODULE'),
  ('pandas.tests.io.parser',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_chunksize',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_chunksize.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_common_basic',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_common_basic.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_data_list',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_data_list.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_decimal',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_decimal.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_file_buffer_url',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_file_buffer_url.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_float',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_float.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_index',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_index.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_inf',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_inf.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_ints',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_ints.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_iterator',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_iterator.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_read_errors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_read_errors.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.common.test_verbose',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_verbose.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.conftest',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.dtypes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.dtypes.test_categorical',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\dtypes\\test_categorical.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.dtypes.test_dtypes_basic',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\dtypes\\test_dtypes_basic.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.dtypes.test_empty',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\dtypes\\test_empty.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_c_parser_only',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_c_parser_only.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_comment',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_comment.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_compression',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_compression.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_concatenate_chunks',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_concatenate_chunks.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_converters',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_converters.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_dialect',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_dialect.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_encoding',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_encoding.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_header',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_header.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_index_col',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_index_col.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_mangle_dupes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_mangle_dupes.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_multi_thread',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_multi_thread.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_na_values',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_na_values.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_network',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_network.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_parse_dates',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_parse_dates.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_python_parser_only',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_python_parser_only.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_quoting',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_quoting.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_read_fwf',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_read_fwf.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_skiprows',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_skiprows.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_textreader',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_textreader.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_unsupported',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_unsupported.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.test_upcast',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_upcast.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.usecols',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\usecols\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.usecols.test_parse_dates',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\usecols\\test_parse_dates.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.usecols.test_strings',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\usecols\\test_strings.py',
   'PYMODULE'),
  ('pandas.tests.io.parser.usecols.test_usecols_basic',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\parser\\usecols\\test_usecols_basic.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\common.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.conftest',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_append',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_append.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_categorical',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_categorical.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_compat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_compat.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_complex',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_complex.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_errors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_errors.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_file_handling',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_file_handling.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_keys',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_keys.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_put',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_put.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_pytables_missing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_pytables_missing.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_read',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_read.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_retain_attributes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_retain_attributes.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_round_trip',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_round_trip.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_select',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_select.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_store',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_store.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_subclass',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_subclass.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_time_series',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_time_series.py',
   'PYMODULE'),
  ('pandas.tests.io.pytables.test_timezones',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_timezones.py',
   'PYMODULE'),
  ('pandas.tests.io.sas',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.sas.test_byteswap',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\sas\\test_byteswap.py',
   'PYMODULE'),
  ('pandas.tests.io.sas.test_sas',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\sas\\test_sas.py',
   'PYMODULE'),
  ('pandas.tests.io.sas.test_sas7bdat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\sas\\test_sas7bdat.py',
   'PYMODULE'),
  ('pandas.tests.io.sas.test_xport',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\sas\\test_xport.py',
   'PYMODULE'),
  ('pandas.tests.io.test_clipboard',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\test_clipboard.py',
   'PYMODULE'),
  ('pandas.tests.io.test_common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\test_common.py',
   'PYMODULE'),
  ('pandas.tests.io.test_compression',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\test_compression.py',
   'PYMODULE'),
  ('pandas.tests.io.test_feather',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\test_feather.py',
   'PYMODULE'),
  ('pandas.tests.io.test_fsspec',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\test_fsspec.py',
   'PYMODULE'),
  ('pandas.tests.io.test_gbq',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\test_gbq.py',
   'PYMODULE'),
  ('pandas.tests.io.test_gcs',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\test_gcs.py',
   'PYMODULE'),
  ('pandas.tests.io.test_html',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\test_html.py',
   'PYMODULE'),
  ('pandas.tests.io.test_http_headers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\test_http_headers.py',
   'PYMODULE'),
  ('pandas.tests.io.test_orc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\test_orc.py',
   'PYMODULE'),
  ('pandas.tests.io.test_parquet',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\test_parquet.py',
   'PYMODULE'),
  ('pandas.tests.io.test_pickle',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\test_pickle.py',
   'PYMODULE'),
  ('pandas.tests.io.test_s3',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\test_s3.py',
   'PYMODULE'),
  ('pandas.tests.io.test_spss',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\test_spss.py',
   'PYMODULE'),
  ('pandas.tests.io.test_sql',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\test_sql.py',
   'PYMODULE'),
  ('pandas.tests.io.test_stata',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\test_stata.py',
   'PYMODULE'),
  ('pandas.tests.io.xml',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\xml\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.io.xml.conftest',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\xml\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.io.xml.test_to_xml',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\xml\\test_to_xml.py',
   'PYMODULE'),
  ('pandas.tests.io.xml.test_xml',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\xml\\test_xml.py',
   'PYMODULE'),
  ('pandas.tests.io.xml.test_xml_dtypes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\io\\xml\\test_xml_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.libs',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\libs\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.libs.test_hashtable',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\libs\\test_hashtable.py',
   'PYMODULE'),
  ('pandas.tests.libs.test_join',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\libs\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.libs.test_lib',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\libs\\test_lib.py',
   'PYMODULE'),
  ('pandas.tests.libs.test_libalgos',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\libs\\test_libalgos.py',
   'PYMODULE'),
  ('pandas.tests.plotting',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.plotting.common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\plotting\\common.py',
   'PYMODULE'),
  ('pandas.tests.plotting.conftest',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\plotting\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.plotting.frame',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\plotting\\frame\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.plotting.frame.test_frame',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\plotting\\frame\\test_frame.py',
   'PYMODULE'),
  ('pandas.tests.plotting.frame.test_frame_color',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\plotting\\frame\\test_frame_color.py',
   'PYMODULE'),
  ('pandas.tests.plotting.frame.test_frame_groupby',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\plotting\\frame\\test_frame_groupby.py',
   'PYMODULE'),
  ('pandas.tests.plotting.frame.test_frame_legend',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\plotting\\frame\\test_frame_legend.py',
   'PYMODULE'),
  ('pandas.tests.plotting.frame.test_frame_subplots',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\plotting\\frame\\test_frame_subplots.py',
   'PYMODULE'),
  ('pandas.tests.plotting.frame.test_hist_box_by',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\plotting\\frame\\test_hist_box_by.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_backend',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\plotting\\test_backend.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_boxplot_method',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\plotting\\test_boxplot_method.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\plotting\\test_common.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_converter',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\plotting\\test_converter.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_datetimelike',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\plotting\\test_datetimelike.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_groupby',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\plotting\\test_groupby.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_hist_method',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\plotting\\test_hist_method.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_misc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\plotting\\test_misc.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_series',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\plotting\\test_series.py',
   'PYMODULE'),
  ('pandas.tests.plotting.test_style',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\plotting\\test_style.py',
   'PYMODULE'),
  ('pandas.tests.reductions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\reductions\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.reductions.test_reductions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\reductions\\test_reductions.py',
   'PYMODULE'),
  ('pandas.tests.reductions.test_stat_reductions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\reductions\\test_stat_reductions.py',
   'PYMODULE'),
  ('pandas.tests.resample',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\resample\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.resample.conftest',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\resample\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.resample.test_base',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\resample\\test_base.py',
   'PYMODULE'),
  ('pandas.tests.resample.test_datetime_index',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\resample\\test_datetime_index.py',
   'PYMODULE'),
  ('pandas.tests.resample.test_period_index',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\resample\\test_period_index.py',
   'PYMODULE'),
  ('pandas.tests.resample.test_resample_api',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\resample\\test_resample_api.py',
   'PYMODULE'),
  ('pandas.tests.resample.test_resampler_grouper',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\resample\\test_resampler_grouper.py',
   'PYMODULE'),
  ('pandas.tests.resample.test_time_grouper',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\resample\\test_time_grouper.py',
   'PYMODULE'),
  ('pandas.tests.resample.test_timedelta',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\resample\\test_timedelta.py',
   'PYMODULE'),
  ('pandas.tests.reshape',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.conftest',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_append',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_append.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_append_common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_append_common.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_categorical',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_categorical.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_concat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_concat.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_dataframe',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_dataframe.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_datetimes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_datetimes.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_empty',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_empty.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_index',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_index.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_invalid',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_invalid.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_series',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_series.py',
   'PYMODULE'),
  ('pandas.tests.reshape.concat.test_sort',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_sort.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\reshape\\merge\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge.test_join',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\reshape\\merge\\test_join.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge.test_merge',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\reshape\\merge\\test_merge.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge.test_merge_asof',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\reshape\\merge\\test_merge_asof.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge.test_merge_cross',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\reshape\\merge\\test_merge_cross.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge.test_merge_index_as_string',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\reshape\\merge\\test_merge_index_as_string.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge.test_merge_ordered',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\reshape\\merge\\test_merge_ordered.py',
   'PYMODULE'),
  ('pandas.tests.reshape.merge.test_multi',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\reshape\\merge\\test_multi.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_crosstab',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\reshape\\test_crosstab.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_cut',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\reshape\\test_cut.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_from_dummies',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\reshape\\test_from_dummies.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_get_dummies',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\reshape\\test_get_dummies.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_melt',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\reshape\\test_melt.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_pivot',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\reshape\\test_pivot.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_pivot_multilevel',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\reshape\\test_pivot_multilevel.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_qcut',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\reshape\\test_qcut.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_union_categoricals',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\reshape\\test_union_categoricals.py',
   'PYMODULE'),
  ('pandas.tests.reshape.test_util',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\reshape\\test_util.py',
   'PYMODULE'),
  ('pandas.tests.scalar',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.scalar.interval',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\interval\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.scalar.interval.test_arithmetic',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\interval\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.scalar.interval.test_constructors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\interval\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.scalar.interval.test_contains',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\interval\\test_contains.py',
   'PYMODULE'),
  ('pandas.tests.scalar.interval.test_formats',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\interval\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.scalar.interval.test_interval',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\interval\\test_interval.py',
   'PYMODULE'),
  ('pandas.tests.scalar.interval.test_overlaps',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\interval\\test_overlaps.py',
   'PYMODULE'),
  ('pandas.tests.scalar.period',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\period\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.scalar.period.test_arithmetic',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\period\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.scalar.period.test_asfreq',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\period\\test_asfreq.py',
   'PYMODULE'),
  ('pandas.tests.scalar.period.test_period',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\period\\test_period.py',
   'PYMODULE'),
  ('pandas.tests.scalar.test_na_scalar',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\test_na_scalar.py',
   'PYMODULE'),
  ('pandas.tests.scalar.test_nat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\test_nat.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timedelta',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\timedelta\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timedelta.methods',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\timedelta\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timedelta.methods.test_as_unit',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\timedelta\\methods\\test_as_unit.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timedelta.methods.test_round',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\timedelta\\methods\\test_round.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timedelta.test_arithmetic',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\timedelta\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timedelta.test_constructors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\timedelta\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timedelta.test_formats',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\timedelta\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timedelta.test_timedelta',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\timedelta\\test_timedelta.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods.test_as_unit',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\test_as_unit.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods.test_normalize',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\test_normalize.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods.test_replace',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\test_replace.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods.test_round',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\test_round.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods.test_timestamp_method',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\test_timestamp_method.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods.test_to_julian_date',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\test_to_julian_date.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods.test_to_pydatetime',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\test_to_pydatetime.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods.test_tz_convert',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\test_tz_convert.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.methods.test_tz_localize',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\test_tz_localize.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.test_arithmetic',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.test_comparisons',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\test_comparisons.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.test_constructors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.test_formats',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.test_timestamp',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\test_timestamp.py',
   'PYMODULE'),
  ('pandas.tests.scalar.timestamp.test_timezones',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\test_timezones.py',
   'PYMODULE'),
  ('pandas.tests.series',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.series.accessors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\accessors\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.series.accessors.test_cat_accessor',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\accessors\\test_cat_accessor.py',
   'PYMODULE'),
  ('pandas.tests.series.accessors.test_dt_accessor',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\accessors\\test_dt_accessor.py',
   'PYMODULE'),
  ('pandas.tests.series.accessors.test_list_accessor',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\accessors\\test_list_accessor.py',
   'PYMODULE'),
  ('pandas.tests.series.accessors.test_sparse_accessor',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\accessors\\test_sparse_accessor.py',
   'PYMODULE'),
  ('pandas.tests.series.accessors.test_str_accessor',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\accessors\\test_str_accessor.py',
   'PYMODULE'),
  ('pandas.tests.series.accessors.test_struct_accessor',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\accessors\\test_struct_accessor.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_datetime',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_datetime.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_delitem',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_delitem.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_get',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_get.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_getitem',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_getitem.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_indexing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_indexing.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_mask',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_mask.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_set_value',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_set_value.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_setitem',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_setitem.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_take',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_take.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_where',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_where.py',
   'PYMODULE'),
  ('pandas.tests.series.indexing.test_xs',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_xs.py',
   'PYMODULE'),
  ('pandas.tests.series.methods',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_add_prefix_suffix',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_add_prefix_suffix.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_align',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_align.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_argsort',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_argsort.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_asof',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_asof.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_astype',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_astype.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_autocorr',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_autocorr.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_between',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_between.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_case_when',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_case_when.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_clip',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_clip.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_combine',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_combine.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_combine_first',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_combine_first.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_compare',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_compare.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_convert_dtypes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_convert_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_copy',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_copy.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_count',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_count.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_cov_corr',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_cov_corr.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_describe',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_describe.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_diff',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_diff.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_drop',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_drop.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_drop_duplicates',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_drop_duplicates.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_dropna',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_dropna.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_dtypes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_duplicated',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_duplicated.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_equals',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_equals.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_explode',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_explode.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_fillna',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_fillna.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_get_numeric_data',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_get_numeric_data.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_head_tail',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_head_tail.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_infer_objects',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_infer_objects.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_info',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_info.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_interpolate',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_interpolate.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_is_monotonic',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_is_monotonic.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_is_unique',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_is_unique.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_isin',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_isin.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_isna',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_isna.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_item',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_item.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_map',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_map.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_matmul',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_matmul.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_nlargest',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_nlargest.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_nunique',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_nunique.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_pct_change',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_pct_change.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_pop',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_pop.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_quantile',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_quantile.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_rank',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_rank.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_reindex',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_reindex.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_reindex_like',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_reindex_like.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_rename',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_rename.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_rename_axis',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_rename_axis.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_repeat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_repeat.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_replace',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_replace.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_reset_index',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_reset_index.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_round',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_round.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_searchsorted',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_searchsorted.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_set_name',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_set_name.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_size',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_size.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_sort_index',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_sort_index.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_sort_values',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_sort_values.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_to_csv',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_to_csv.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_to_dict',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_to_dict.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_to_frame',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_to_frame.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_to_numpy',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_to_numpy.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_tolist',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_tolist.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_truncate',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_truncate.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_tz_localize',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_tz_localize.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_unique',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_unique.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_unstack',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_unstack.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_update',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_update.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_value_counts',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_value_counts.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_values',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_values.py',
   'PYMODULE'),
  ('pandas.tests.series.methods.test_view',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_view.py',
   'PYMODULE'),
  ('pandas.tests.series.test_api',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\test_api.py',
   'PYMODULE'),
  ('pandas.tests.series.test_arithmetic',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\test_arithmetic.py',
   'PYMODULE'),
  ('pandas.tests.series.test_constructors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\test_constructors.py',
   'PYMODULE'),
  ('pandas.tests.series.test_cumulative',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\test_cumulative.py',
   'PYMODULE'),
  ('pandas.tests.series.test_formats',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\test_formats.py',
   'PYMODULE'),
  ('pandas.tests.series.test_iteration',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\test_iteration.py',
   'PYMODULE'),
  ('pandas.tests.series.test_logical_ops',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\test_logical_ops.py',
   'PYMODULE'),
  ('pandas.tests.series.test_missing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\test_missing.py',
   'PYMODULE'),
  ('pandas.tests.series.test_npfuncs',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\test_npfuncs.py',
   'PYMODULE'),
  ('pandas.tests.series.test_reductions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\test_reductions.py',
   'PYMODULE'),
  ('pandas.tests.series.test_subclass',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\test_subclass.py',
   'PYMODULE'),
  ('pandas.tests.series.test_ufunc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\test_ufunc.py',
   'PYMODULE'),
  ('pandas.tests.series.test_unary',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\test_unary.py',
   'PYMODULE'),
  ('pandas.tests.series.test_validate',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\series\\test_validate.py',
   'PYMODULE'),
  ('pandas.tests.strings',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.strings.conftest',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\strings\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_api',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\strings\\test_api.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_case_justify',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\strings\\test_case_justify.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_cat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\strings\\test_cat.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_extract',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\strings\\test_extract.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_find_replace',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\strings\\test_find_replace.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_get_dummies',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\strings\\test_get_dummies.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_split_partition',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\strings\\test_split_partition.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_string_array',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\strings\\test_string_array.py',
   'PYMODULE'),
  ('pandas.tests.strings.test_strings',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\strings\\test_strings.py',
   'PYMODULE'),
  ('pandas.tests.test_aggregation',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\test_aggregation.py',
   'PYMODULE'),
  ('pandas.tests.test_algos',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\test_algos.py',
   'PYMODULE'),
  ('pandas.tests.test_common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\test_common.py',
   'PYMODULE'),
  ('pandas.tests.test_downstream',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\test_downstream.py',
   'PYMODULE'),
  ('pandas.tests.test_errors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\test_errors.py',
   'PYMODULE'),
  ('pandas.tests.test_expressions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\test_expressions.py',
   'PYMODULE'),
  ('pandas.tests.test_flags',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\test_flags.py',
   'PYMODULE'),
  ('pandas.tests.test_multilevel',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\test_multilevel.py',
   'PYMODULE'),
  ('pandas.tests.test_nanops',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\test_nanops.py',
   'PYMODULE'),
  ('pandas.tests.test_optional_dependency',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\test_optional_dependency.py',
   'PYMODULE'),
  ('pandas.tests.test_register_accessor',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\test_register_accessor.py',
   'PYMODULE'),
  ('pandas.tests.test_sorting',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\test_sorting.py',
   'PYMODULE'),
  ('pandas.tests.test_take',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\test_take.py',
   'PYMODULE'),
  ('pandas.tests.tools',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.tools.test_to_datetime',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tools\\test_to_datetime.py',
   'PYMODULE'),
  ('pandas.tests.tools.test_to_numeric',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tools\\test_to_numeric.py',
   'PYMODULE'),
  ('pandas.tests.tools.test_to_time',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tools\\test_to_time.py',
   'PYMODULE'),
  ('pandas.tests.tools.test_to_timedelta',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tools\\test_to_timedelta.py',
   'PYMODULE'),
  ('pandas.tests.tseries',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.tseries.frequencies',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tseries\\frequencies\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.tseries.frequencies.test_freq_code',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tseries\\frequencies\\test_freq_code.py',
   'PYMODULE'),
  ('pandas.tests.tseries.frequencies.test_frequencies',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tseries\\frequencies\\test_frequencies.py',
   'PYMODULE'),
  ('pandas.tests.tseries.frequencies.test_inference',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tseries\\frequencies\\test_inference.py',
   'PYMODULE'),
  ('pandas.tests.tseries.holiday',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tseries\\holiday\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.tseries.holiday.test_calendar',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tseries\\holiday\\test_calendar.py',
   'PYMODULE'),
  ('pandas.tests.tseries.holiday.test_federal',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tseries\\holiday\\test_federal.py',
   'PYMODULE'),
  ('pandas.tests.tseries.holiday.test_holiday',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tseries\\holiday\\test_holiday.py',
   'PYMODULE'),
  ('pandas.tests.tseries.holiday.test_observance',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tseries\\holiday\\test_observance.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\common.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_business_day',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_business_day.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_business_hour',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_business_hour.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_business_month',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_business_month.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_business_quarter',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_business_quarter.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_business_year',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_business_year.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_common.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_custom_business_day',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_custom_business_day.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_custom_business_hour',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_custom_business_hour.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_custom_business_month',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_custom_business_month.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_dst',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_dst.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_easter',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_easter.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_fiscal',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_fiscal.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_index',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_index.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_month',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_month.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_offsets',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_offsets.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_offsets_properties',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_offsets_properties.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_quarter',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_quarter.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_ticks',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_ticks.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_week',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_week.py',
   'PYMODULE'),
  ('pandas.tests.tseries.offsets.test_year',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_year.py',
   'PYMODULE'),
  ('pandas.tests.tslibs',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_api',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_api.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_array_to_datetime',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_array_to_datetime.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_ccalendar',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_ccalendar.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_conversion',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_conversion.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_fields',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_fields.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_libfrequencies',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_libfrequencies.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_liboffsets',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_liboffsets.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_np_datetime',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_np_datetime.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_npy_units',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_npy_units.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_parse_iso8601',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_parse_iso8601.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_parsing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_parsing.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_period',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_period.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_resolution',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_resolution.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_strptime',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_strptime.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_timedeltas',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_timedeltas.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_timezones',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_timezones.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_to_offset',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_to_offset.py',
   'PYMODULE'),
  ('pandas.tests.tslibs.test_tzconversion',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_tzconversion.py',
   'PYMODULE'),
  ('pandas.tests.util',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.util.conftest',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\util\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_almost_equal',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_almost_equal.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_attr_equal',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_attr_equal.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_categorical_equal',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_categorical_equal.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_extension_array_equal',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_extension_array_equal.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_frame_equal',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_frame_equal.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_index_equal',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_index_equal.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_interval_array_equal',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_interval_array_equal.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_numpy_array_equal',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_numpy_array_equal.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_produces_warning',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_produces_warning.py',
   'PYMODULE'),
  ('pandas.tests.util.test_assert_series_equal',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_series_equal.py',
   'PYMODULE'),
  ('pandas.tests.util.test_deprecate',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\util\\test_deprecate.py',
   'PYMODULE'),
  ('pandas.tests.util.test_deprecate_kwarg',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\util\\test_deprecate_kwarg.py',
   'PYMODULE'),
  ('pandas.tests.util.test_deprecate_nonkeyword_arguments',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\util\\test_deprecate_nonkeyword_arguments.py',
   'PYMODULE'),
  ('pandas.tests.util.test_doc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\util\\test_doc.py',
   'PYMODULE'),
  ('pandas.tests.util.test_hashing',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\util\\test_hashing.py',
   'PYMODULE'),
  ('pandas.tests.util.test_numba',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\util\\test_numba.py',
   'PYMODULE'),
  ('pandas.tests.util.test_rewrite_warning',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\util\\test_rewrite_warning.py',
   'PYMODULE'),
  ('pandas.tests.util.test_shares_memory',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\util\\test_shares_memory.py',
   'PYMODULE'),
  ('pandas.tests.util.test_show_versions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\util\\test_show_versions.py',
   'PYMODULE'),
  ('pandas.tests.util.test_util',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\util\\test_util.py',
   'PYMODULE'),
  ('pandas.tests.util.test_validate_args',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\util\\test_validate_args.py',
   'PYMODULE'),
  ('pandas.tests.util.test_validate_args_and_kwargs',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\util\\test_validate_args_and_kwargs.py',
   'PYMODULE'),
  ('pandas.tests.util.test_validate_inclusive',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\util\\test_validate_inclusive.py',
   'PYMODULE'),
  ('pandas.tests.util.test_validate_kwargs',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\util\\test_validate_kwargs.py',
   'PYMODULE'),
  ('pandas.tests.window',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.window.conftest',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\window\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.window.moments',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\window\\moments\\__init__.py',
   'PYMODULE'),
  ('pandas.tests.window.moments.conftest',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\window\\moments\\conftest.py',
   'PYMODULE'),
  ('pandas.tests.window.moments.test_moments_consistency_ewm',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\window\\moments\\test_moments_consistency_ewm.py',
   'PYMODULE'),
  ('pandas.tests.window.moments.test_moments_consistency_expanding',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\window\\moments\\test_moments_consistency_expanding.py',
   'PYMODULE'),
  ('pandas.tests.window.moments.test_moments_consistency_rolling',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\window\\moments\\test_moments_consistency_rolling.py',
   'PYMODULE'),
  ('pandas.tests.window.test_api',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\window\\test_api.py',
   'PYMODULE'),
  ('pandas.tests.window.test_apply',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\window\\test_apply.py',
   'PYMODULE'),
  ('pandas.tests.window.test_base_indexer',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\window\\test_base_indexer.py',
   'PYMODULE'),
  ('pandas.tests.window.test_cython_aggregations',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\window\\test_cython_aggregations.py',
   'PYMODULE'),
  ('pandas.tests.window.test_dtypes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\window\\test_dtypes.py',
   'PYMODULE'),
  ('pandas.tests.window.test_ewm',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\window\\test_ewm.py',
   'PYMODULE'),
  ('pandas.tests.window.test_expanding',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\window\\test_expanding.py',
   'PYMODULE'),
  ('pandas.tests.window.test_groupby',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\window\\test_groupby.py',
   'PYMODULE'),
  ('pandas.tests.window.test_numba',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\window\\test_numba.py',
   'PYMODULE'),
  ('pandas.tests.window.test_online',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\window\\test_online.py',
   'PYMODULE'),
  ('pandas.tests.window.test_pairwise',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\window\\test_pairwise.py',
   'PYMODULE'),
  ('pandas.tests.window.test_rolling',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\window\\test_rolling.py',
   'PYMODULE'),
  ('pandas.tests.window.test_rolling_functions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\window\\test_rolling_functions.py',
   'PYMODULE'),
  ('pandas.tests.window.test_rolling_quantile',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\window\\test_rolling_quantile.py',
   'PYMODULE'),
  ('pandas.tests.window.test_rolling_skew_kurt',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\window\\test_rolling_skew_kurt.py',
   'PYMODULE'),
  ('pandas.tests.window.test_timeseries_window',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\window\\test_timeseries_window.py',
   'PYMODULE'),
  ('pandas.tests.window.test_win_type',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tests\\window\\test_win_type.py',
   'PYMODULE'),
  ('pandas.tseries',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.util',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.util._doctools',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\util\\_doctools.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.util._test_decorators',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\util\\_test_decorators.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util.version',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pathlib',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\pathlib\\__init__.py',
   'PYMODULE'),
  ('pathlib._abc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\pathlib\\_abc.py',
   'PYMODULE'),
  ('pathlib._local',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\pathlib\\_local.py',
   'PYMODULE'),
  ('pdb', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\pdb.py', 'PYMODULE'),
  ('pefile',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pefile.py',
   'PYMODULE'),
  ('pickle', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\pickle.py', 'PYMODULE'),
  ('pickletools',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\pickletools.py',
   'PYMODULE'),
  ('pkg_resources',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkgutil',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\pkgutil.py',
   'PYMODULE'),
  ('platform',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\platform.py',
   'PYMODULE'),
  ('plistlib',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\plistlib.py',
   'PYMODULE'),
  ('pprint', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\py_compile.py',
   'PYMODULE'),
  ('pydoc', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pypinyin',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\__init__.py',
   'PYMODULE'),
  ('pypinyin.__main__',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\__main__.py',
   'PYMODULE'),
  ('pypinyin.__pyinstaller',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\__pyinstaller\\__init__.py',
   'PYMODULE'),
  ('pypinyin.__pyinstaller.hook-pypinyin',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\__pyinstaller\\hook-pypinyin.py',
   'PYMODULE'),
  ('pypinyin.compat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\compat.py',
   'PYMODULE'),
  ('pypinyin.constants',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\constants.py',
   'PYMODULE'),
  ('pypinyin.contrib',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\contrib\\__init__.py',
   'PYMODULE'),
  ('pypinyin.contrib._tone_rule',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\contrib\\_tone_rule.py',
   'PYMODULE'),
  ('pypinyin.contrib.mmseg',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\contrib\\mmseg.py',
   'PYMODULE'),
  ('pypinyin.contrib.neutral_tone',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\contrib\\neutral_tone.py',
   'PYMODULE'),
  ('pypinyin.contrib.tone_convert',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\contrib\\tone_convert.py',
   'PYMODULE'),
  ('pypinyin.contrib.tone_sandhi',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\contrib\\tone_sandhi.py',
   'PYMODULE'),
  ('pypinyin.contrib.uv',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\contrib\\uv.py',
   'PYMODULE'),
  ('pypinyin.converter',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\converter.py',
   'PYMODULE'),
  ('pypinyin.core',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\core.py',
   'PYMODULE'),
  ('pypinyin.exceptions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\exceptions.py',
   'PYMODULE'),
  ('pypinyin.phonetic_symbol',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\phonetic_symbol.py',
   'PYMODULE'),
  ('pypinyin.phrases_dict',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\phrases_dict.py',
   'PYMODULE'),
  ('pypinyin.pinyin_dict',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\pinyin_dict.py',
   'PYMODULE'),
  ('pypinyin.runner',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\runner.py',
   'PYMODULE'),
  ('pypinyin.seg',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\seg\\__init__.py',
   'PYMODULE'),
  ('pypinyin.seg.mmseg',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\seg\\mmseg.py',
   'PYMODULE'),
  ('pypinyin.seg.simpleseg',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\seg\\simpleseg.py',
   'PYMODULE'),
  ('pypinyin.standard',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\standard.py',
   'PYMODULE'),
  ('pypinyin.style',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\style\\__init__.py',
   'PYMODULE'),
  ('pypinyin.style._constants',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\style\\_constants.py',
   'PYMODULE'),
  ('pypinyin.style._tone_convert',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\style\\_tone_convert.py',
   'PYMODULE'),
  ('pypinyin.style._tone_rule',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\style\\_tone_rule.py',
   'PYMODULE'),
  ('pypinyin.style._utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\style\\_utils.py',
   'PYMODULE'),
  ('pypinyin.style.bopomofo',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\style\\bopomofo.py',
   'PYMODULE'),
  ('pypinyin.style.cyrillic',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\style\\cyrillic.py',
   'PYMODULE'),
  ('pypinyin.style.finals',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\style\\finals.py',
   'PYMODULE'),
  ('pypinyin.style.gwoyeu',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\style\\gwoyeu.py',
   'PYMODULE'),
  ('pypinyin.style.initials',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\style\\initials.py',
   'PYMODULE'),
  ('pypinyin.style.others',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\style\\others.py',
   'PYMODULE'),
  ('pypinyin.style.tone',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\style\\tone.py',
   'PYMODULE'),
  ('pypinyin.style.wadegiles',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\style\\wadegiles.py',
   'PYMODULE'),
  ('pypinyin.tools',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\tools\\__init__.py',
   'PYMODULE'),
  ('pypinyin.tools.toneconvert',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\tools\\toneconvert.py',
   'PYMODULE'),
  ('pypinyin.utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pypinyin\\utils.py',
   'PYMODULE'),
  ('pytz',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('queue', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\random.py', 'PYMODULE'),
  ('rlcompleter',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('runpy', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\runpy.py', 'PYMODULE'),
  ('selectors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\selectors.py',
   'PYMODULE'),
  ('setuptools',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._functools',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\_functools.py',
   'PYMODULE'),
  ('setuptools._distutils._itertools',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\_itertools.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils._vendor',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\_vendor\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._vendor.packaging',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._vendor.packaging._elffile',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._distutils._vendor.packaging._manylinux',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._distutils._vendor.packaging._musllinux',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._distutils._vendor.packaging._structures',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._distutils._vendor.packaging.tags',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._distutils._vendor.packaging.utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._distutils._vendor.packaging.version',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py38',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.config',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._adapters',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._itertools',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.abc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.compat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.compat.py38',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.compat.py39',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.functional',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\functional.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.future',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\future\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.future.adapters',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\future\\adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.readers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.ordered_set',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\ordered_set\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.android',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.api',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.macos',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.unix',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.version',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.windows',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\signal.py', 'PYMODULE'),
  ('site', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site.py', 'PYMODULE'),
  ('six',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\six.py',
   'PYMODULE'),
  ('socket', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\socketserver.py',
   'PYMODULE'),
  ('sqlite3',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.__main__',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\sqlite3\\__main__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('ssl', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\statistics.py',
   'PYMODULE'),
  ('string', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\string.py', 'PYMODULE'),
  ('stringprep',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\sysconfig\\__init__.py',
   'PYMODULE'),
  ('tarfile',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\threading.py',
   'PYMODULE'),
  ('tkinter',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.scrolledtext',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\tkinter\\scrolledtext.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('token', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\token.py', 'PYMODULE'),
  ('tokenize',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\tokenize.py',
   'PYMODULE'),
  ('tomllib',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\tomllib\\__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._re',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\tomllib\\_re.py',
   'PYMODULE'),
  ('tomllib._types',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\tomllib\\_types.py',
   'PYMODULE'),
  ('tracemalloc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\typing.py', 'PYMODULE'),
  ('tzdata',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\tzdata\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\tzdata\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Africa',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Argentina',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Indiana',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Kentucky',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Kentucky\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.North_Dakota',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\North_Dakota\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Antarctica',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Arctic',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\tzdata\\zoneinfo\\Arctic\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Asia',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Atlantic',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Australia',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Brazil',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\tzdata\\zoneinfo\\Brazil\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Canada',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\tzdata\\zoneinfo\\Canada\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Chile',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\tzdata\\zoneinfo\\Chile\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Etc',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Europe',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Indian',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Mexico',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\tzdata\\zoneinfo\\Mexico\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Pacific',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.US',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\__init__.py',
   'PYMODULE'),
  ('urllib',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('uuid', 'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\uuid.py', 'PYMODULE'),
  ('webbrowser',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('wheel',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\__init__.py',
   'PYMODULE'),
  ('wheel.cli',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('wheel.cli.convert',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('wheel.cli.pack',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('wheel.cli.tags',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('wheel.cli.unpack',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('wheel.macosx_libfile',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('wheel.metadata',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\metadata.py',
   'PYMODULE'),
  ('wheel.util',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\util.py',
   'PYMODULE'),
  ('wheel.vendored',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('wheel.vendored.packaging',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._elffile',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._manylinux',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._musllinux',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._parser',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._structures',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._tokenizer',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.markers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.requirements',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.specifiers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.tags',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.utils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.version',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('wheel.wheelfile',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('win32ctypes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\win32ctypes\\__init__.py',
   'PYMODULE'),
  ('win32ctypes.core',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\win32ctypes\\core\\__init__.py',
   'PYMODULE'),
  ('win32ctypes.core._winerrors',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\win32ctypes\\core\\_winerrors.py',
   'PYMODULE'),
  ('win32ctypes.core.compat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\win32ctypes\\core\\compat.py',
   'PYMODULE'),
  ('win32ctypes.core.ctypes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\win32ctypes\\core\\ctypes\\__init__.py',
   'PYMODULE'),
  ('win32ctypes.core.ctypes._authentication',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\win32ctypes\\core\\ctypes\\_authentication.py',
   'PYMODULE'),
  ('win32ctypes.core.ctypes._common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\win32ctypes\\core\\ctypes\\_common.py',
   'PYMODULE'),
  ('win32ctypes.core.ctypes._dll',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\win32ctypes\\core\\ctypes\\_dll.py',
   'PYMODULE'),
  ('win32ctypes.core.ctypes._nl_support',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\win32ctypes\\core\\ctypes\\_nl_support.py',
   'PYMODULE'),
  ('win32ctypes.core.ctypes._resource',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\win32ctypes\\core\\ctypes\\_resource.py',
   'PYMODULE'),
  ('win32ctypes.core.ctypes._system_information',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\win32ctypes\\core\\ctypes\\_system_information.py',
   'PYMODULE'),
  ('win32ctypes.core.ctypes._time',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\win32ctypes\\core\\ctypes\\_time.py',
   'PYMODULE'),
  ('win32ctypes.core.ctypes._util',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\win32ctypes\\core\\ctypes\\_util.py',
   'PYMODULE'),
  ('win32ctypes.pywin32',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\win32ctypes\\pywin32\\__init__.py',
   'PYMODULE'),
  ('win32ctypes.pywin32.pywintypes',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\win32ctypes\\pywin32\\pywintypes.py',
   'PYMODULE'),
  ('win32ctypes.pywin32.win32api',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\win32ctypes\\pywin32\\win32api.py',
   'PYMODULE'),
  ('win32ctypes.pywin32.win32cred',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\win32ctypes\\pywin32\\win32cred.py',
   'PYMODULE'),
  ('win32ctypes.version',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\site-packages\\win32ctypes\\version.py',
   'PYMODULE'),
  ('xml',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.dom',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('zipfile',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\zipimport.py',
   'PYMODULE'),
  ('zoneinfo',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('zoneinfo._common',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\zoneinfo\\_common.py',
   'PYMODULE'),
  ('zoneinfo._tzpath',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\zoneinfo\\_tzpath.py',
   'PYMODULE'),
  ('zoneinfo._zoneinfo',
   'D:\\anaconda3\\envs\\dabaozhuanyong\\Lib\\zoneinfo\\_zoneinfo.py',
   'PYMODULE')])
