@echo off
setlocal enabledelayedexpansion

:: 设置控制台编码为UTF-8
chcp 65001 > nul

:: 显示欢迎信息
echo.
echo ====================================================
echo      Medical System Build Tool v2.0
echo ====================================================
echo.

:: 显示当前时间
echo Start time: %date% %time%
echo.

:: 第一步：环境检查
echo Step 1/8: Check environment...
echo --------------------------------

:: 检查Python环境
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python not found!
    pause
    exit /b 1
)
echo Python OK: 
python --version

:: 检查PyInstaller
pyinstaller --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: PyInstaller not found!
    echo Installing PyInstaller...
    pip install pyinstaller
    if %errorlevel% neq 0 (
        echo PyInstaller installation failed!
        pause
        exit /b 1
    )
)
echo PyInstaller OK: 
pyinstaller --version

echo.

:: 第二步：文件检查
echo Step 2/8: Check files...
echo --------------------------------

:: 检查主程序文件
if not exist "main_gui.py" (
    echo ERROR: main_gui.py not found!
    pause
    exit /b 1
)
echo main_gui.py OK

:: 检查CSV文件
set "csv_file=ceshi_real data_converted.csv"
if not exist "%csv_file%" (
    echo ERROR: %csv_file% not found!
    pause
    exit /b 1
)
echo CSV file OK: %csv_file%

echo.

:: 第三步：创建运行库工具
echo Step 3/8: Create runtime tools...
echo --------------------------------

:: 创建运行库检查脚本
echo Creating runtime check script...
echo import sys > runtime_check.py
echo import os >> runtime_check.py
echo import platform >> runtime_check.py
echo print("System check completed") >> runtime_check.py

echo Runtime check script created

:: 创建运行库下载脚本
echo Creating download script...
if not exist "runtime_libraries" mkdir "runtime_libraries"
echo @echo off > download_libraries.bat
echo echo Downloading runtime libraries... >> download_libraries.bat
echo powershell -Command "Invoke-WebRequest -Uri 'https://aka.ms/vs/17/release/vc_redist.x64.exe' -OutFile 'runtime_libraries\vc_redist_2015-2022_x64.exe'" >> download_libraries.bat
echo powershell -Command "Invoke-WebRequest -Uri 'https://aka.ms/vs/17/release/vc_redist.x86.exe' -OutFile 'runtime_libraries\vc_redist_2015-2022_x86.exe'" >> download_libraries.bat
echo echo Download completed >> download_libraries.bat
echo pause >> download_libraries.bat

echo Download script created

:: 创建运行库安装脚本
echo Creating install script...
echo @echo off > install_libraries.bat
echo echo Installing runtime libraries... >> install_libraries.bat
echo if exist "runtime_libraries\vc_redist_2015-2022_x64.exe" "runtime_libraries\vc_redist_2015-2022_x64.exe" /quiet /norestart >> install_libraries.bat
echo if exist "runtime_libraries\vc_redist_2015-2022_x86.exe" "runtime_libraries\vc_redist_2015-2022_x86.exe" /quiet /norestart >> install_libraries.bat
echo echo Installation completed >> install_libraries.bat
echo pause >> install_libraries.bat

echo Install script created

echo.

:: 第四步：清理旧文件
echo Step 4/8: Clean old files...
echo --------------------------------

if exist "dist" (
    echo Removing dist folder...
    rmdir /s /q "dist"
    echo dist folder cleaned
)

if exist "build" (
    echo Removing build folder...
    rmdir /s /q "build"
    echo build folder cleaned
)

:: 清理旧的spec文件
for %%f in (*.spec) do (
    if exist "%%f" (
        echo Removing spec file: %%f
        del /q "%%f"
    )
)

echo Old files cleaned
echo.

:: 第五步：选择打包模式
echo Step 5/8: Select build mode...
echo --------------------------------
echo Please select build mode:
echo 1. Compatible mode (recommended) - includes all runtime libraries
echo 2. Standard mode - balanced compatibility and file size  
echo 3. Light mode - minimal file size
echo 4. Windows 7 mode - optimized for Windows 7
echo.
set /p "pack_mode=Enter option (1/2/3/4): "

if "%pack_mode%"=="4" (
    echo Selected Windows 7 mode
    set "windows7_mode=true"
) else if "%pack_mode%"=="1" (
    echo Selected compatible mode
    set "compatibility_mode=true"
) else if "%pack_mode%"=="3" (
    echo Selected light mode
    set "lightweight_mode=true"
) else (
    echo Selected standard mode
    set "standard_mode=true"
)

echo.

:: 第六步：收集系统DLL
echo Step 6/8: Collect system DLLs...
echo --------------------------------

if not exist "system_dlls" mkdir "system_dlls"

if "%compatibility_mode%"=="true" (
    echo Collecting system DLLs...
    copy "C:\Windows\System32\ucrtbase.dll" "system_dlls\" >nul 2>&1
    copy "C:\Windows\System32\msvcp140.dll" "system_dlls\" >nul 2>&1
    copy "C:\Windows\System32\vcruntime140.dll" "system_dlls\" >nul 2>&1
    echo System DLLs collected
) else (
    echo Skipping DLL collection for this mode
)

echo.

:: 第七步：开始打包
echo Step 7/8: Start building...
echo --------------------------------
echo Building, please wait...
echo.

:: 根据选择的模式执行不同的打包命令
if "%windows7_mode%"=="true" (
    echo Building with Windows 7 mode...
    
    :: 为Windows 7收集更多必要的DLL
    echo Collecting Windows 7 specific DLLs...
    copy "C:\Windows\System32\ucrtbase.dll" "system_dlls\" >nul 2>&1
    copy "C:\Windows\System32\msvcp140.dll" "system_dlls\" >nul 2>&1
    copy "C:\Windows\System32\vcruntime140.dll" "system_dlls\" >nul 2>&1
    copy "C:\Windows\System32\kernel32.dll" "system_dlls\" >nul 2>&1
    copy "C:\Windows\System32\api-ms-win-*.dll" "system_dlls\" >nul 2>&1
    
    :: 使用更兼容的打包参数
    pyinstaller --onefile --noconsole --name "Medical_System" ^
        --add-data "%csv_file%;." ^
        --add-data "runtime_check.py;." ^
        --add-data "system_dlls;system_dlls" ^
        --hidden-import pandas ^
        --hidden-import openpyxl ^
        --hidden-import pypinyin ^
        --hidden-import tkinter ^
        --hidden-import tkinter.ttk ^
        --hidden-import tkinter.filedialog ^
        --hidden-import tkinter.messagebox ^
        --hidden-import tkinter.scrolledtext ^
        --hidden-import concurrent.futures ^
        --hidden-import threading ^
        --hidden-import json ^
        --hidden-import datetime ^
        --hidden-import difflib ^
        --hidden-import glob ^
        --hidden-import re ^
        --hidden-import time ^
        --hidden-import platform ^
        --hidden-import sys ^
        --collect-all pandas ^
        --collect-all openpyxl ^
        --collect-all pypinyin ^
        --exclude-module matplotlib ^
        --exclude-module numpy.testing ^
        --exclude-module test ^
        --exclude-module unittest ^
        --exclude-module asyncio ^
        --exclude-module multiprocessing ^
        --noupx ^
        --disable-windowed-traceback ^
        --target-arch=x86_64 ^
        main_gui.py
) else if "%compatibility_mode%"=="true" (
    echo Building with compatible mode...
    pyinstaller --onefile --noconsole --name "Medical_System" --add-data "%csv_file%;." --add-data "runtime_check.py;." --add-data "system_dlls;system_dlls" --hidden-import pandas --hidden-import openpyxl --hidden-import pypinyin --hidden-import tkinter --hidden-import tkinter.ttk --hidden-import tkinter.filedialog --hidden-import tkinter.messagebox --hidden-import tkinter.scrolledtext --hidden-import concurrent.futures --hidden-import threading --hidden-import json --hidden-import datetime --hidden-import difflib --hidden-import glob --hidden-import re --hidden-import time --hidden-import platform --hidden-import ctypes --hidden-import winreg --hidden-import subprocess --collect-all pandas --collect-all openpyxl --collect-all pypinyin --exclude-module matplotlib --exclude-module numpy.testing --exclude-module test --exclude-module unittest --noupx main_gui.py
) else if "%lightweight_mode%"=="true" (
    echo Building with light mode...
    pyinstaller --onefile --noconsole --name "Medical_System" --add-data "%csv_file%;." --add-data "runtime_check.py;." --hidden-import pandas --hidden-import openpyxl --hidden-import pypinyin --hidden-import tkinter --hidden-import tkinter.ttk --hidden-import tkinter.filedialog --hidden-import tkinter.messagebox --hidden-import tkinter.scrolledtext --hidden-import concurrent.futures --hidden-import threading --hidden-import json --hidden-import datetime --hidden-import difflib --hidden-import glob --hidden-import re --hidden-import time --exclude-module matplotlib --exclude-module numpy.testing --exclude-module test --exclude-module unittest --strip main_gui.py
) else (
    echo Building with standard mode...
    pyinstaller --onefile --noconsole --name "Medical_System" --add-data "%csv_file%;." --add-data "runtime_check.py;." --hidden-import pandas --hidden-import openpyxl --hidden-import pypinyin --hidden-import tkinter --hidden-import tkinter.ttk --hidden-import tkinter.filedialog --hidden-import tkinter.messagebox --hidden-import tkinter.scrolledtext --hidden-import concurrent.futures --hidden-import threading --hidden-import json --hidden-import datetime --hidden-import difflib --hidden-import glob --hidden-import re --hidden-import time --hidden-import platform --hidden-import ctypes --exclude-module matplotlib --exclude-module numpy.testing --noupx main_gui.py
)

:: 检查打包是否成功
if %errorlevel% neq 0 (
    echo.
    echo Build failed!
    echo Please check the error messages above
    pause
    exit /b 1
)

echo.

:: 第八步：创建发布包
echo Step 8/8: Create release package...
echo --------------------------------

set "exe_file=dist\Medical_System.exe"

if exist "%exe_file%" (
    echo Executable file generated successfully!
    echo File location: %exe_file%
    
    :: 显示文件大小
    for %%i in ("%exe_file%") do (
        set "file_size=%%~zi"
        echo File size: !file_size! bytes
    )
    
    :: 显示文件创建时间
    for %%i in ("%exe_file%") do echo Created: %%~ti
) else (
    echo ERROR: Executable file not found!
    pause
    exit /b 1
)

:: 创建release文件夹
if not exist "release" mkdir "release"
echo Release folder created

:: 复制可执行文件
copy "%exe_file%" "release\" >nul
echo Executable copied to release folder

:: 创建records文件夹
if not exist "release\records" mkdir "release\records"
echo Records folder created

:: 复制运行库相关文件
copy "runtime_check.py" "release\" >nul 2>&1
copy "download_libraries.bat" "release\" >nul 2>&1
copy "install_libraries.bat" "release\" >nul 2>&1
echo Runtime tools copied to release folder

:: 复制运行库文件夹（如果存在）
if exist "runtime_libraries" (
    if not exist "release\runtime_libraries" mkdir "release\runtime_libraries"
    xcopy "runtime_libraries\*" "release\runtime_libraries\" /s /y >nul 2>&1
    echo Runtime libraries copied to release folder
)

:: 如果存在示例数据，复制到release
if exist "records" (
    xcopy "records\*" "release\records\" /s /y >nul 2>&1
    echo Sample data copied to release\records
)

:: 创建使用说明
echo Creating user manual...
echo Medical System v2.0 > "release\README.txt"
echo =================== >> "release\README.txt"
echo. >> "release\README.txt"
echo Usage Instructions: >> "release\README.txt"
echo 1. Run install_libraries.bat first >> "release\README.txt"
echo 2. Run Medical_System.exe >> "release\README.txt"
echo 3. Load Excel/CSV files from records folder >> "release\README.txt"
echo. >> "release\README.txt"
echo Build mode: %pack_mode% >> "release\README.txt"
echo Build time: %date% %time% >> "release\README.txt"

echo User manual created

:: 创建Windows 7专用文件
if "%windows7_mode%"=="true" (
    echo Creating Windows 7 specific files...
    echo Windows 7 Compatibility Notes > "release\Windows7_Notes.txt"
    echo ============================ >> "release\Windows7_Notes.txt"
    echo This version is optimized for Windows 7 >> "release\Windows7_Notes.txt"
    echo Please install all runtime libraries before use >> "release\Windows7_Notes.txt"
    echo Windows 7 specific files created
)

echo.

:: 完成信息
echo ====================================================
echo                Build Complete!
echo ====================================================
echo.
echo Release package location: release folder
echo Package contents:
echo    - Medical_System.exe
echo    - install_libraries.bat
echo    - download_libraries.bat
echo    - runtime_check.py
echo    - runtime_libraries\ (if downloaded)
echo    - records\ (user data folder)
echo    - README.txt
echo.
echo You can now distribute the release folder to users
echo.

:: 询问是否立即测试
set /p "test_now=Test run the program now? (Y/N): "
if /i "%test_now%"=="Y" (
    echo.
    echo Starting program for testing...
    start "" "release\Medical_System.exe"
)

echo.
echo Completion time: %date% %time%
echo Thank you for using the Medical System Build Tool!
echo.
pause

:: 创建 start_medical_system_win7.bat
@echo off
title 医疗系统 - Windows 7 兼容启动器
chcp 65001 > nul

echo 正在启动医疗系统（Windows 7兼容模式）...
echo.

:: 检查运行库
if not exist "runtime_libraries" (
    echo 警告：未找到运行库文件，可能导致启动失败
    echo 建议先运行"一键解决运行库问题.bat"
    pause
)

:: 设置兼容性环境变量
set PYTHONIOENCODING=utf-8
set PYTHONLEGACYWINDOWSSTDIO=1

:: 启动程序
start "" "Medical_System.exe"

echo 程序已启动
echo 如果遇到问题，请查看"Windows7_Notes.txt"
pause
