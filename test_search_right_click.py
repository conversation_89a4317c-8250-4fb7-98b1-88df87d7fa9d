#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk

class TestSearchRightClick:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("搜索框右键菜单测试")
        self.root.geometry("600x400")
        
        # 模拟复制的病例数据
        self.copied_case_data = {
            'name': '张三',
            'age': '50',
            'gender': '男',
            'address': '北京',
            'contact': '13456789012',
            'symptoms': '口渴',
            'condition': '多饮多尿',
            'diagnosis': '糖尿病',
            'prescription': '吃点抗糖药',
            'usage': '一天三次，内服。',
            'prescriber': '未知'
        }
        
        self.create_widgets()
    
    def create_widgets(self):
        # 创建搜索区域
        search_frame = ttk.LabelFrame(self.root, text="查找相似病例", padding=10)
        search_frame.pack(fill='x', pady=10, padx=10)
        
        # 姓名输入
        ttk.Label(search_frame, text="姓名:").grid(row=0, column=0, sticky='w', pady=2)
        self.search_name_var = tk.StringVar()
        name_entry = ttk.Entry(search_frame, textvariable=self.search_name_var, width=40)
        name_entry.grid(row=0, column=1, sticky='ew', padx=(5, 0), pady=2)
        self.setup_search_right_click_menu(name_entry, 'search_name_var')
        
        # 性别输入
        ttk.Label(search_frame, text="性别:").grid(row=0, column=2, sticky='w', pady=2, padx=(10, 0))
        self.search_gender_var = tk.StringVar()
        gender_entry = ttk.Entry(search_frame, textvariable=self.search_gender_var, width=20)
        gender_entry.grid(row=0, column=3, sticky='ew', padx=(5, 0), pady=2)
        self.setup_search_right_click_menu(gender_entry, 'search_gender_var')
        
        # 住址输入
        ttk.Label(search_frame, text="住址:").grid(row=1, column=0, sticky='w', pady=2)
        self.search_address_var = tk.StringVar()
        address_entry = ttk.Entry(search_frame, textvariable=self.search_address_var, width=40)
        address_entry.grid(row=1, column=1, columnspan=3, sticky='ew', padx=(5, 0), pady=2)
        self.setup_search_right_click_menu(address_entry, 'search_address_var')
        
        # 症状输入
        ttk.Label(search_frame, text="症状:").grid(row=2, column=0, sticky='w', pady=2)
        self.symptoms_var = tk.StringVar()
        symptoms_entry = ttk.Entry(search_frame, textvariable=self.symptoms_var, width=40)
        symptoms_entry.grid(row=2, column=1, columnspan=3, sticky='ew', padx=(5, 0), pady=2)
        self.setup_search_right_click_menu(symptoms_entry, 'symptoms_var')
        
        # 配置网格权重
        search_frame.columnconfigure(1, weight=1)
        search_frame.columnconfigure(3, weight=1)
        
        # 说明文字
        info_label = ttk.Label(self.root, text="右键点击搜索框查看粘贴菜单\n模拟病例数据：张三，男，北京，口渴", 
                              foreground='blue', justify='center')
        info_label.pack(pady=20)
        
        # 清空按钮
        clear_button = ttk.Button(self.root, text="清空所有搜索框", command=self.clear_all)
        clear_button.pack(pady=10)
    
    def setup_search_right_click_menu(self, widget, field_name):
        """为搜索输入控件设置右键菜单"""
        def show_search_context_menu(event):
            # 只有在有复制数据时才显示菜单
            if not self.copied_case_data:
                return
            
            # 创建右键菜单
            context_menu = tk.Menu(self.root, tearoff=0)
            
            # 根据搜索字段类型添加相应的粘贴选项
            search_field_mapping = {
                'search_name_var': ('姓名', 'name'),
                'search_gender_var': ('性别', 'gender'),
                'search_address_var': ('住址', 'address'),
                'symptoms_var': ('症状', 'symptoms')
            }
            
            if field_name in search_field_mapping:
                field_display_name, field_key = search_field_mapping[field_name]
                field_value = self.copied_case_data.get(field_key, '')
                
                if field_value:
                    context_menu.add_command(
                        label=f"粘贴{field_display_name}: {field_value[:20]}{'...' if len(str(field_value)) > 20 else ''}",
                        command=lambda: self.paste_search_field_data(widget, field_name, field_key)
                    )
            
            # 添加粘贴所有搜索信息的选项
            context_menu.add_separator()
            context_menu.add_command(
                label="粘贴基本搜索信息",
                command=lambda: self.paste_all_search_data()
            )
            
            # 显示菜单
            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()
        
        # 绑定右键事件
        widget.bind("<Button-3>", show_search_context_menu)  # Windows右键
    
    def paste_search_field_data(self, widget, field_name, field_key):
        """粘贴单个搜索字段的数据"""
        if not self.copied_case_data:
            return
        
        field_value = self.copied_case_data.get(field_key, '')
        if not field_value:
            return
        
        try:
            # 根据字段名设置对应的StringVar
            if field_name == 'search_name_var':
                self.search_name_var.set(str(field_value))
            elif field_name == 'search_gender_var':
                self.search_gender_var.set(str(field_value))
            elif field_name == 'search_address_var':
                self.search_address_var.set(str(field_value))
            elif field_name == 'symptoms_var':
                # 对于症状，可能需要合并症状和病情
                symptoms = self.copied_case_data.get('symptoms', '').strip()
                condition = self.copied_case_data.get('condition', '').strip()
                
                if symptoms and condition and symptoms != condition:
                    combined_symptoms = f"{symptoms} {condition}"
                else:
                    combined_symptoms = symptoms or condition
                
                self.symptoms_var.set(combined_symptoms)
                        
        except Exception as e:
            print(f"粘贴搜索数据时出错: {str(e)}")
    
    def paste_all_search_data(self):
        """粘贴所有搜索相关数据"""
        if not self.copied_case_data:
            return
        
        try:
            case = self.copied_case_data
            
            # 填充搜索框
            self.search_name_var.set(case.get('name', ''))
            self.search_gender_var.set(case.get('gender', ''))
            self.search_address_var.set(case.get('address', ''))
            
            # 合并症状和病情到症状搜索框
            symptoms = case.get('symptoms', '').strip()
            condition = case.get('condition', '').strip()
            
            if symptoms and condition and symptoms != condition:
                combined_symptoms = f"{symptoms} {condition}"
            else:
                combined_symptoms = symptoms or condition
            
            self.symptoms_var.set(combined_symptoms)
            
        except Exception as e:
            print(f"粘贴所有搜索数据时出错: {str(e)}")
    
    def clear_all(self):
        """清空所有搜索框"""
        self.search_name_var.set('')
        self.search_gender_var.set('')
        self.search_address_var.set('')
        self.symptoms_var.set('')
    
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    app = TestSearchRightClick()
    app.run()
