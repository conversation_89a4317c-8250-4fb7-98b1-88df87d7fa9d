(['E:\\code\\yiyaoxinxichuli\\main_gui.py'],
 ['E:\\code\\yiyaoxinxichuli'],
 ['pandas',
  'openpyxl',
  'pypinyin',
  'tkinter',
  'tkinter.ttk',
  'tkinter.filedialog',
  'tkinter.messagebox',
  'tkinter.scrolledtext',
  'concurrent.futures',
  'threading',
  'json',
  'datetime',
  'difflib',
  'glob',
  're',
  'time',
  'platform',
  'ctypes',
  'secrets'],
 [('D:\\anaconda3\\envs\\win7\\Lib\\site-packages\\numpy\\_pyinstaller', 0),
  ('D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\__pyinstaller', 0),
  ('D:\\anaconda3\\envs\\win7\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('D:\\anaconda3\\envs\\win7\\lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 ['matplotlib', 'numpy.testing', '__main__'],
 [],
 False,
 {},
 0,
 [],
 [('ceshi_real data_converted.csv',
   'E:\\code\\yiyaoxinxichuli\\ceshi_real data_converted.csv',
   'DATA'),
  ('runtime_check.py', 'E:\\code\\yiyaoxinxichuli\\runtime_check.py', 'DATA'),
  ('system_dlls\\kernel32.dll',
   'E:\\code\\yiyaoxinxichuli\\system_dlls\\kernel32.dll',
   'DATA'),
  ('system_dlls\\msvcp140.dll',
   'E:\\code\\yiyaoxinxichuli\\system_dlls\\msvcp140.dll',
   'DATA'),
  ('system_dlls\\ucrtbase.dll',
   'E:\\code\\yiyaoxinxichuli\\system_dlls\\ucrtbase.dll',
   'DATA'),
  ('system_dlls\\vcruntime140.dll',
   'E:\\code\\yiyaoxinxichuli\\system_dlls\\vcruntime140.dll',
   'DATA')],
 '3.8.10 (default, May 19 2021, 13:12:57) [MSC v.1916 64 bit (AMD64)]',
 [('pyi_rth_pkgutil',
   'D:\\anaconda3\\envs\\win7\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\anaconda3\\envs\\win7\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\anaconda3\\envs\\win7\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'D:\\anaconda3\\envs\\win7\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('main_gui', 'E:\\code\\yiyaoxinxichuli\\main_gui.py', 'PYSOURCE')],
 [('subprocess', 'D:\\anaconda3\\envs\\win7\\lib\\subprocess.py', 'PYMODULE'),
  ('selectors', 'D:\\anaconda3\\envs\\win7\\lib\\selectors.py', 'PYMODULE'),
  ('contextlib', 'D:\\anaconda3\\envs\\win7\\lib\\contextlib.py', 'PYMODULE'),
  ('signal', 'D:\\anaconda3\\envs\\win7\\lib\\signal.py', 'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\anaconda3\\envs\\win7\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\anaconda3\\envs\\win7\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\anaconda3\\envs\\win7\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\anaconda3\\envs\\win7\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\anaconda3\\envs\\win7\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\anaconda3\\envs\\win7\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'D:\\anaconda3\\envs\\win7\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc', 'D:\\anaconda3\\envs\\win7\\lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('gzip', 'D:\\anaconda3\\envs\\win7\\lib\\gzip.py', 'PYMODULE'),
  ('argparse', 'D:\\anaconda3\\envs\\win7\\lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'D:\\anaconda3\\envs\\win7\\lib\\textwrap.py', 'PYMODULE'),
  ('copy', 'D:\\anaconda3\\envs\\win7\\lib\\copy.py', 'PYMODULE'),
  ('gettext', 'D:\\anaconda3\\envs\\win7\\lib\\gettext.py', 'PYMODULE'),
  ('_compression',
   'D:\\anaconda3\\envs\\win7\\lib\\_compression.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\anaconda3\\envs\\win7\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\anaconda3\\envs\\win7\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml', 'D:\\anaconda3\\envs\\win7\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\anaconda3\\envs\\win7\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\anaconda3\\envs\\win7\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\anaconda3\\envs\\win7\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('fnmatch', 'D:\\anaconda3\\envs\\win7\\lib\\fnmatch.py', 'PYMODULE'),
  ('getpass', 'D:\\anaconda3\\envs\\win7\\lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'D:\\anaconda3\\envs\\win7\\lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'D:\\anaconda3\\envs\\win7\\lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'D:\\anaconda3\\envs\\win7\\lib\\netrc.py', 'PYMODULE'),
  ('shlex', 'D:\\anaconda3\\envs\\win7\\lib\\shlex.py', 'PYMODULE'),
  ('mimetypes', 'D:\\anaconda3\\envs\\win7\\lib\\mimetypes.py', 'PYMODULE'),
  ('getopt', 'D:\\anaconda3\\envs\\win7\\lib\\getopt.py', 'PYMODULE'),
  ('email.utils',
   'D:\\anaconda3\\envs\\win7\\lib\\email\\utils.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\anaconda3\\envs\\win7\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\anaconda3\\envs\\win7\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('quopri', 'D:\\anaconda3\\envs\\win7\\lib\\quopri.py', 'PYMODULE'),
  ('email.errors',
   'D:\\anaconda3\\envs\\win7\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\anaconda3\\envs\\win7\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\anaconda3\\envs\\win7\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\anaconda3\\envs\\win7\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar', 'D:\\anaconda3\\envs\\win7\\lib\\calendar.py', 'PYMODULE'),
  ('random', 'D:\\anaconda3\\envs\\win7\\lib\\random.py', 'PYMODULE'),
  ('http.cookiejar',
   'D:\\anaconda3\\envs\\win7\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http', 'D:\\anaconda3\\envs\\win7\\lib\\http\\__init__.py', 'PYMODULE'),
  ('urllib', 'D:\\anaconda3\\envs\\win7\\lib\\urllib\\__init__.py', 'PYMODULE'),
  ('ssl', 'D:\\anaconda3\\envs\\win7\\lib\\ssl.py', 'PYMODULE'),
  ('urllib.response',
   'D:\\anaconda3\\envs\\win7\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'D:\\anaconda3\\envs\\win7\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('string', 'D:\\anaconda3\\envs\\win7\\lib\\string.py', 'PYMODULE'),
  ('hashlib', 'D:\\anaconda3\\envs\\win7\\lib\\hashlib.py', 'PYMODULE'),
  ('email', 'D:\\anaconda3\\envs\\win7\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser',
   'D:\\anaconda3\\envs\\win7\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\anaconda3\\envs\\win7\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\anaconda3\\envs\\win7\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.message',
   'D:\\anaconda3\\envs\\win7\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\anaconda3\\envs\\win7\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\anaconda3\\envs\\win7\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\anaconda3\\envs\\win7\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\anaconda3\\envs\\win7\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\anaconda3\\envs\\win7\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'D:\\anaconda3\\envs\\win7\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('uu', 'D:\\anaconda3\\envs\\win7\\lib\\uu.py', 'PYMODULE'),
  ('optparse', 'D:\\anaconda3\\envs\\win7\\lib\\optparse.py', 'PYMODULE'),
  ('email._header_value_parser',
   'D:\\anaconda3\\envs\\win7\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.header',
   'D:\\anaconda3\\envs\\win7\\lib\\email\\header.py',
   'PYMODULE'),
  ('bisect', 'D:\\anaconda3\\envs\\win7\\lib\\bisect.py', 'PYMODULE'),
  ('xml.sax',
   'D:\\anaconda3\\envs\\win7\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\anaconda3\\envs\\win7\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\anaconda3\\envs\\win7\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\anaconda3\\envs\\win7\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\anaconda3\\envs\\win7\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('http.client',
   'D:\\anaconda3\\envs\\win7\\lib\\http\\client.py',
   'PYMODULE'),
  ('decimal', 'D:\\anaconda3\\envs\\win7\\lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\anaconda3\\envs\\win7\\lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'D:\\anaconda3\\envs\\win7\\lib\\contextvars.py', 'PYMODULE'),
  ('numbers', 'D:\\anaconda3\\envs\\win7\\lib\\numbers.py', 'PYMODULE'),
  ('base64', 'D:\\anaconda3\\envs\\win7\\lib\\base64.py', 'PYMODULE'),
  ('hmac', 'D:\\anaconda3\\envs\\win7\\lib\\hmac.py', 'PYMODULE'),
  ('struct', 'D:\\anaconda3\\envs\\win7\\lib\\struct.py', 'PYMODULE'),
  ('socket', 'D:\\anaconda3\\envs\\win7\\lib\\socket.py', 'PYMODULE'),
  ('tempfile', 'D:\\anaconda3\\envs\\win7\\lib\\tempfile.py', 'PYMODULE'),
  ('shutil', 'D:\\anaconda3\\envs\\win7\\lib\\shutil.py', 'PYMODULE'),
  ('zipfile', 'D:\\anaconda3\\envs\\win7\\lib\\zipfile.py', 'PYMODULE'),
  ('py_compile', 'D:\\anaconda3\\envs\\win7\\lib\\py_compile.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\anaconda3\\envs\\win7\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib',
   'D:\\anaconda3\\envs\\win7\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\anaconda3\\envs\\win7\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\anaconda3\\envs\\win7\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\anaconda3\\envs\\win7\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\anaconda3\\envs\\win7\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('configparser',
   'D:\\anaconda3\\envs\\win7\\lib\\configparser.py',
   'PYMODULE'),
  ('pathlib', 'D:\\anaconda3\\envs\\win7\\lib\\pathlib.py', 'PYMODULE'),
  ('csv', 'D:\\anaconda3\\envs\\win7\\lib\\csv.py', 'PYMODULE'),
  ('tokenize', 'D:\\anaconda3\\envs\\win7\\lib\\tokenize.py', 'PYMODULE'),
  ('token', 'D:\\anaconda3\\envs\\win7\\lib\\token.py', 'PYMODULE'),
  ('importlib.util',
   'D:\\anaconda3\\envs\\win7\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('tarfile', 'D:\\anaconda3\\envs\\win7\\lib\\tarfile.py', 'PYMODULE'),
  ('lzma', 'D:\\anaconda3\\envs\\win7\\lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'D:\\anaconda3\\envs\\win7\\lib\\bz2.py', 'PYMODULE'),
  ('logging',
   'D:\\anaconda3\\envs\\win7\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle', 'D:\\anaconda3\\envs\\win7\\lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\anaconda3\\envs\\win7\\lib\\pprint.py', 'PYMODULE'),
  ('_compat_pickle',
   'D:\\anaconda3\\envs\\win7\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\anaconda3\\envs\\win7\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\anaconda3\\envs\\win7\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\anaconda3\\envs\\win7\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\anaconda3\\envs\\win7\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\anaconda3\\envs\\win7\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\anaconda3\\envs\\win7\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\anaconda3\\envs\\win7\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\anaconda3\\envs\\win7\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\anaconda3\\envs\\win7\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\anaconda3\\envs\\win7\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue', 'D:\\anaconda3\\envs\\win7\\lib\\queue.py', 'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\anaconda3\\envs\\win7\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\anaconda3\\envs\\win7\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\anaconda3\\envs\\win7\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\anaconda3\\envs\\win7\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\anaconda3\\envs\\win7\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\anaconda3\\envs\\win7\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('runpy', 'D:\\anaconda3\\envs\\win7\\lib\\runpy.py', 'PYMODULE'),
  ('pkgutil', 'D:\\anaconda3\\envs\\win7\\lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'D:\\anaconda3\\envs\\win7\\lib\\zipimport.py', 'PYMODULE'),
  ('inspect', 'D:\\anaconda3\\envs\\win7\\lib\\inspect.py', 'PYMODULE'),
  ('ast', 'D:\\anaconda3\\envs\\win7\\lib\\ast.py', 'PYMODULE'),
  ('dis', 'D:\\anaconda3\\envs\\win7\\lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\anaconda3\\envs\\win7\\lib\\opcode.py', 'PYMODULE'),
  ('multiprocessing',
   'D:\\anaconda3\\envs\\win7\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('secrets', 'D:\\anaconda3\\envs\\win7\\lib\\secrets.py', 'PYMODULE'),
  ('ctypes', 'D:\\anaconda3\\envs\\win7\\lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\anaconda3\\envs\\win7\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\anaconda3\\envs\\win7\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\anaconda3\\envs\\win7\\lib\\_py_abc.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\anaconda3\\envs\\win7\\lib\\tracemalloc.py', 'PYMODULE'),
  ('stringprep', 'D:\\anaconda3\\envs\\win7\\lib\\stringprep.py', 'PYMODULE'),
  ('platform', 'D:\\anaconda3\\envs\\win7\\lib\\platform.py', 'PYMODULE'),
  ('_strptime', 'D:\\anaconda3\\envs\\win7\\lib\\_strptime.py', 'PYMODULE'),
  ('pypinyin',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\__init__.py',
   'PYMODULE'),
  ('pypinyin.core',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\core.py',
   'PYMODULE'),
  ('pypinyin.seg.simpleseg',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\seg\\simpleseg.py',
   'PYMODULE'),
  ('pypinyin.seg.mmseg',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\seg\\mmseg.py',
   'PYMODULE'),
  ('pypinyin.seg',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\seg\\__init__.py',
   'PYMODULE'),
  ('pypinyin.contrib.tone_convert',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\contrib\\tone_convert.py',
   'PYMODULE'),
  ('pypinyin.contrib',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\contrib\\__init__.py',
   'PYMODULE'),
  ('pypinyin.style._tone_convert',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\style\\_tone_convert.py',
   'PYMODULE'),
  ('pypinyin.style',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\style\\__init__.py',
   'PYMODULE'),
  ('pypinyin.style.gwoyeu',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\style\\gwoyeu.py',
   'PYMODULE'),
  ('pypinyin.style.others',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\style\\others.py',
   'PYMODULE'),
  ('pypinyin.style.wadegiles',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\style\\wadegiles.py',
   'PYMODULE'),
  ('pypinyin.style.cyrillic',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\style\\cyrillic.py',
   'PYMODULE'),
  ('pypinyin.style.bopomofo',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\style\\bopomofo.py',
   'PYMODULE'),
  ('pypinyin.style.finals',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\style\\finals.py',
   'PYMODULE'),
  ('pypinyin.style.initials',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\style\\initials.py',
   'PYMODULE'),
  ('pypinyin.style._utils',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\style\\_utils.py',
   'PYMODULE'),
  ('pypinyin.standard',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\standard.py',
   'PYMODULE'),
  ('pypinyin.style.tone',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\style\\tone.py',
   'PYMODULE'),
  ('pypinyin.style._constants',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\style\\_constants.py',
   'PYMODULE'),
  ('pypinyin.style._tone_rule',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\style\\_tone_rule.py',
   'PYMODULE'),
  ('pypinyin.contrib.tone_sandhi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\contrib\\tone_sandhi.py',
   'PYMODULE'),
  ('pypinyin.converter',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\converter.py',
   'PYMODULE'),
  ('pypinyin.utils',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\utils.py',
   'PYMODULE'),
  ('pypinyin.exceptions',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\exceptions.py',
   'PYMODULE'),
  ('pypinyin.contrib.neutral_tone',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\contrib\\neutral_tone.py',
   'PYMODULE'),
  ('pypinyin.contrib._tone_rule',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\contrib\\_tone_rule.py',
   'PYMODULE'),
  ('pypinyin.contrib.uv',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\contrib\\uv.py',
   'PYMODULE'),
  ('pypinyin.phonetic_symbol',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\phonetic_symbol.py',
   'PYMODULE'),
  ('pypinyin.constants',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\constants.py',
   'PYMODULE'),
  ('pypinyin.phrases_dict',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\phrases_dict.py',
   'PYMODULE'),
  ('pypinyin.pinyin_dict',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\pinyin_dict.py',
   'PYMODULE'),
  ('pypinyin.compat',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\compat.py',
   'PYMODULE'),
  ('__future__', 'D:\\anaconda3\\envs\\win7\\lib\\__future__.py', 'PYMODULE'),
  ('concurrent.futures',
   'D:\\anaconda3\\envs\\win7\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\anaconda3\\envs\\win7\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\anaconda3\\envs\\win7\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\anaconda3\\envs\\win7\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'D:\\anaconda3\\envs\\win7\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\anaconda3\\envs\\win7\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\anaconda3\\envs\\win7\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\anaconda3\\envs\\win7\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\anaconda3\\envs\\win7\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree',
   'D:\\anaconda3\\envs\\win7\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('numpy',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.version',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy._version',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\_version.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.compat._inspect',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\compat\\_inspect.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('_dummy_thread',
   'D:\\anaconda3\\envs\\win7\\lib\\_dummy_thread.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.ma',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.random',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy._typing',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._generic_alias',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\_typing\\_generic_alias.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.fft',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.linalg',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.lib',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.core.records',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('pydoc', 'D:\\anaconda3\\envs\\win7\\lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser', 'D:\\anaconda3\\envs\\win7\\lib\\webbrowser.py', 'PYMODULE'),
  ('http.server',
   'D:\\anaconda3\\envs\\win7\\lib\\http\\server.py',
   'PYMODULE'),
  ('socketserver',
   'D:\\anaconda3\\envs\\win7\\lib\\socketserver.py',
   'PYMODULE'),
  ('html', 'D:\\anaconda3\\envs\\win7\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'D:\\anaconda3\\envs\\win7\\lib\\html\\entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\anaconda3\\envs\\win7\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'D:\\anaconda3\\envs\\win7\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty', 'D:\\anaconda3\\envs\\win7\\lib\\tty.py', 'PYMODULE'),
  ('sysconfig', 'D:\\anaconda3\\envs\\win7\\lib\\sysconfig.py', 'PYMODULE'),
  ('numpy.lib.polynomial',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.compat',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy.__config__',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy.array_api',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.typing',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('dataclasses', 'D:\\anaconda3\\envs\\win7\\lib\\dataclasses.py', 'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy._globals',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('json', 'D:\\anaconda3\\envs\\win7\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder',
   'D:\\anaconda3\\envs\\win7\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'D:\\anaconda3\\envs\\win7\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\anaconda3\\envs\\win7\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('datetime', 'D:\\anaconda3\\envs\\win7\\lib\\datetime.py', 'PYMODULE'),
  ('threading', 'D:\\anaconda3\\envs\\win7\\lib\\threading.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\anaconda3\\envs\\win7\\lib\\_threading_local.py',
   'PYMODULE'),
  ('typing', 'D:\\anaconda3\\envs\\win7\\lib\\typing.py', 'PYMODULE'),
  ('difflib', 'D:\\anaconda3\\envs\\win7\\lib\\difflib.py', 'PYMODULE'),
  ('glob', 'D:\\anaconda3\\envs\\win7\\lib\\glob.py', 'PYMODULE'),
  ('pandas',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._typing',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.methods',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\ops\\methods.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.util.version',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pandas.util',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.dtype',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\dtype.py',
   'PYMODULE'),
  ('pandas.io._util',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.util._str_methods',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\util\\_str_methods.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.latex',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\formats\\latex.py',
   'PYMODULE'),
  ('doctest', 'D:\\anaconda3\\envs\\win7\\lib\\doctest.py', 'PYMODULE'),
  ('unittest',
   'D:\\anaconda3\\envs\\win7\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\anaconda3\\envs\\win7\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'D:\\anaconda3\\envs\\win7\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\anaconda3\\envs\\win7\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\anaconda3\\envs\\win7\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\anaconda3\\envs\\win7\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.case',
   'D:\\anaconda3\\envs\\win7\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.async_case',
   'D:\\anaconda3\\envs\\win7\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('asyncio',
   'D:\\anaconda3\\envs\\win7\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\anaconda3\\envs\\win7\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   'D:\\anaconda3\\envs\\win7\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\anaconda3\\envs\\win7\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\anaconda3\\envs\\win7\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\anaconda3\\envs\\win7\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\anaconda3\\envs\\win7\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\anaconda3\\envs\\win7\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\anaconda3\\envs\\win7\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\anaconda3\\envs\\win7\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\anaconda3\\envs\\win7\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\anaconda3\\envs\\win7\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\anaconda3\\envs\\win7\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\anaconda3\\envs\\win7\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\anaconda3\\envs\\win7\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\anaconda3\\envs\\win7\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\anaconda3\\envs\\win7\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\anaconda3\\envs\\win7\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\anaconda3\\envs\\win7\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\anaconda3\\envs\\win7\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\anaconda3\\envs\\win7\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\anaconda3\\envs\\win7\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\anaconda3\\envs\\win7\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\anaconda3\\envs\\win7\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\anaconda3\\envs\\win7\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\anaconda3\\envs\\win7\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\anaconda3\\envs\\win7\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\anaconda3\\envs\\win7\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('unittest.result',
   'D:\\anaconda3\\envs\\win7\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.util',
   'D:\\anaconda3\\envs\\win7\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('pdb', 'D:\\anaconda3\\envs\\win7\\lib\\pdb.py', 'PYMODULE'),
  ('code', 'D:\\anaconda3\\envs\\win7\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\anaconda3\\envs\\win7\\lib\\codeop.py', 'PYMODULE'),
  ('bdb', 'D:\\anaconda3\\envs\\win7\\lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'D:\\anaconda3\\envs\\win7\\lib\\cmd.py', 'PYMODULE'),
  ('pandas.core.indexes.multi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.common',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('dateutil.parser',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('six', 'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\six.py', 'PYMODULE'),
  ('dateutil.tz',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.easter',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('fractions', 'D:\\anaconda3\\envs\\win7\\lib\\fractions.py', 'PYMODULE'),
  ('dateutil._common',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._version',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('pytz',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('pytz.lazy',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.api.types',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('uuid', 'D:\\anaconda3\\envs\\win7\\lib\\uuid.py', 'PYMODULE'),
  ('ctypes.util',
   'D:\\anaconda3\\envs\\win7\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes._aix',
   'D:\\anaconda3\\envs\\win7\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\anaconda3\\envs\\win7\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\anaconda3\\envs\\win7\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\anaconda3\\envs\\win7\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\anaconda3\\envs\\win7\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.common',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.base',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas._config.config',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.core.window',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core.series',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('sqlite3',
   'D:\\anaconda3\\envs\\win7\\lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'D:\\anaconda3\\envs\\win7\\lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'D:\\anaconda3\\envs\\win7\\lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('pandas.io.json',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'D:\\anaconda3\\envs\\win7\\lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'D:\\anaconda3\\envs\\win7\\lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'D:\\anaconda3\\envs\\win7\\lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'D:\\anaconda3\\envs\\win7\\lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'D:\\anaconda3\\envs\\win7\\lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'D:\\anaconda3\\envs\\win7\\lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'D:\\anaconda3\\envs\\win7\\lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom',
   'D:\\anaconda3\\envs\\win7\\lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._version',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.api',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.html',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.testing',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas._testing',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing._random',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_testing\\_random.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas.plotting',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.io',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.errors',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.api',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.tseries',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.dtype',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\dtype.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.api',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas._config',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.display',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._libs',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('tkinter.scrolledtext',
   'D:\\anaconda3\\envs\\win7\\lib\\tkinter\\scrolledtext.py',
   'PYMODULE'),
  ('tkinter.constants',
   'D:\\anaconda3\\envs\\win7\\lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'D:\\anaconda3\\envs\\win7\\lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'D:\\anaconda3\\envs\\win7\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'D:\\anaconda3\\envs\\win7\\lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'D:\\anaconda3\\envs\\win7\\lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'D:\\anaconda3\\envs\\win7\\lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'D:\\anaconda3\\envs\\win7\\lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('tkinter',
   'D:\\anaconda3\\envs\\win7\\lib\\tkinter\\__init__.py',
   'PYMODULE')],
 [('system_dlls\\kernel32.dll',
   'E:\\code\\yiyaoxinxichuli\\system_dlls\\kernel32.dll',
   'BINARY'),
  ('system_dlls\\msvcp140.dll',
   'E:\\code\\yiyaoxinxichuli\\system_dlls\\msvcp140.dll',
   'BINARY'),
  ('system_dlls\\ucrtbase.dll',
   'E:\\code\\yiyaoxinxichuli\\system_dlls\\ucrtbase.dll',
   'BINARY'),
  ('system_dlls\\vcruntime140.dll',
   'E:\\code\\yiyaoxinxichuli\\system_dlls\\vcruntime140.dll',
   'BINARY'),
  ('python38.dll', 'D:\\anaconda3\\envs\\win7\\python38.dll', 'BINARY'),
  ('numpy\\.libs\\libopenblas64__v0.3.21-gcc_10_3_0.dll',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\.libs\\libopenblas64__v0.3.21-gcc_10_3_0.dll',
   'BINARY'),
  ('select.pyd', 'D:\\anaconda3\\envs\\win7\\DLLs\\select.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\anaconda3\\envs\\win7\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'D:\\anaconda3\\envs\\win7\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'D:\\anaconda3\\envs\\win7\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\anaconda3\\envs\\win7\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\anaconda3\\envs\\win7\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'D:\\anaconda3\\envs\\win7\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_socket.pyd', 'D:\\anaconda3\\envs\\win7\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'D:\\anaconda3\\envs\\win7\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\anaconda3\\envs\\win7\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_queue.pyd', 'D:\\anaconda3\\envs\\win7\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'D:\\anaconda3\\envs\\win7\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_elementtree.pyd',
   'D:\\anaconda3\\envs\\win7\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\core\\_multiarray_umath.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\random\\mtrand.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\random\\_sfc64.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\random\\_philox.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\random\\_pcg64.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\random\\_mt19937.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\random\\bit_generator.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\random\\_generator.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\random\\_common.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\writers.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\writers.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\anaconda3\\envs\\win7\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'D:\\anaconda3\\envs\\win7\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'D:\\anaconda3\\envs\\win7\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\indexers.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\window\\indexers.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\aggregations.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\window\\aggregations.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\vectorized.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\tslibs\\vectorized.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\tzconversion.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\tslibs\\tzconversion.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timezones.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\tslibs\\timezones.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timestamps.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\tslibs\\timestamps.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timedeltas.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\tslibs\\timedeltas.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\strptime.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\tslibs\\strptime.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\period.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\tslibs\\period.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\parsing.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\tslibs\\parsing.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\offsets.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\tslibs\\offsets.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\np_datetime.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\tslibs\\np_datetime.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\nattype.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\tslibs\\nattype.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\fields.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\tslibs\\fields.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\dtypes.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\tslibs\\dtypes.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\conversion.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\tslibs\\conversion.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\ccalendar.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\tslibs\\ccalendar.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\base.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\tslibs\\base.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\testing.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\testing.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sparse.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\sparse.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reshape.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\reshape.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reduction.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\reduction.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\properties.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\properties.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\parsers.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\parsers.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops_dispatch.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\ops_dispatch.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\ops.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\missing.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\missing.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\json.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\json.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\join.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\join.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\interval.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\interval.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\internals.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\internals.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\indexing.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\indexing.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\index.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\index.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashing.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\hashing.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\groupby.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\groupby.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\arrays.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\arrays.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\algos.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\algos.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\io\\sas\\_sas.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\sas\\_sas.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\io\\sas\\_byteswap.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\sas\\_byteswap.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslib.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\tslib.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\lib.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\lib.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashtable.cp38-win_amd64.pyd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\hashtable.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('_tkinter.pyd',
   'D:\\anaconda3\\envs\\win7\\DLLs\\_tkinter.pyd',
   'EXTENSION'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-2-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-core-console-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-eventing-provider-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-eventing-provider-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'D:\\anaconda3\\envs\\win7\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'E:\\code\\yiyaoxinxichuli\\system_dlls\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'D:\\anaconda3\\envs\\win7\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-1_1-x64.dll',
   'D:\\anaconda3\\envs\\win7\\Library\\bin\\libcrypto-1_1-x64.dll',
   'BINARY'),
  ('libssl-1_1-x64.dll',
   'D:\\anaconda3\\envs\\win7\\Library\\bin\\libssl-1_1-x64.dll',
   'BINARY'),
  ('libffi-7.dll', 'D:\\anaconda3\\envs\\win7\\DLLs\\libffi-7.dll', 'BINARY'),
  ('sqlite3.dll',
   'D:\\anaconda3\\envs\\win7\\Library\\bin\\sqlite3.dll',
   'BINARY'),
  ('pandas\\_libs\\window\\VCRUNTIME140_1.dll',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\window\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('pandas\\_libs\\window\\MSVCP140.dll',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\_libs\\window\\MSVCP140.dll',
   'BINARY'),
  ('tk86t.dll', 'D:\\anaconda3\\envs\\win7\\DLLs\\tk86t.dll', 'BINARY'),
  ('tcl86t.dll', 'D:\\anaconda3\\envs\\win7\\DLLs\\tcl86t.dll', 'BINARY'),
  ('ucrtbase.dll', 'D:\\anaconda3\\envs\\win7\\ucrtbase.dll', 'BINARY')],
 [],
 [],
 [('ceshi_real data_converted.csv',
   'E:\\code\\yiyaoxinxichuli\\ceshi_real data_converted.csv',
   'DATA'),
  ('runtime_check.py', 'E:\\code\\yiyaoxinxichuli\\runtime_check.py', 'DATA'),
  ('pypinyin\\style\\_tone_convert.pyi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\style\\_tone_convert.pyi',
   'DATA'),
  ('pypinyin\\style\\others.pyi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\style\\others.pyi',
   'DATA'),
  ('pypinyin\\contrib\\tone_sandhi.pyi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\contrib\\tone_sandhi.pyi',
   'DATA'),
  ('pypinyin\\runner.pyi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\runner.pyi',
   'DATA'),
  ('pypinyin\\phrases_dict.pyi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\phrases_dict.pyi',
   'DATA'),
  ('pypinyin\\style\\tone.pyi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\style\\tone.pyi',
   'DATA'),
  ('pypinyin\\style\\_tone_rule.pyi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\style\\_tone_rule.pyi',
   'DATA'),
  ('pypinyin\\__init__.pyi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\__init__.pyi',
   'DATA'),
  ('pypinyin\\standard.pyi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\standard.pyi',
   'DATA'),
  ('pypinyin\\contrib\\_tone_rule.pyi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\contrib\\_tone_rule.pyi',
   'DATA'),
  ('pypinyin\\py.typed',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\py.typed',
   'DATA'),
  ('pypinyin\\contrib\\tone_convert.pyi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\contrib\\tone_convert.pyi',
   'DATA'),
  ('pypinyin\\constants.pyi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\constants.pyi',
   'DATA'),
  ('pypinyin\\style\\__init__.pyi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\style\\__init__.pyi',
   'DATA'),
  ('pypinyin\\style\\gwoyeu.pyi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\style\\gwoyeu.pyi',
   'DATA'),
  ('pypinyin\\style\\initials.pyi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\style\\initials.pyi',
   'DATA'),
  ('pypinyin\\contrib\\uv.pyi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\contrib\\uv.pyi',
   'DATA'),
  ('pypinyin\\style\\bopomofo.pyi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\style\\bopomofo.pyi',
   'DATA'),
  ('pypinyin\\style\\_utils.pyi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\style\\_utils.pyi',
   'DATA'),
  ('pypinyin\\phrases_dict.json',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\phrases_dict.json',
   'DATA'),
  ('pypinyin\\pinyin_dict.pyi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\pinyin_dict.pyi',
   'DATA'),
  ('pypinyin\\style\\_constants.pyi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\style\\_constants.pyi',
   'DATA'),
  ('pypinyin\\utils.pyi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\utils.pyi',
   'DATA'),
  ('pypinyin\\style\\cyrillic.pyi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\style\\cyrillic.pyi',
   'DATA'),
  ('pypinyin\\converter.pyi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\converter.pyi',
   'DATA'),
  ('pypinyin\\style\\finals.pyi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\style\\finals.pyi',
   'DATA'),
  ('pypinyin\\seg\\mmseg.pyi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\seg\\mmseg.pyi',
   'DATA'),
  ('pypinyin\\pinyin_dict.json',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\pinyin_dict.json',
   'DATA'),
  ('pypinyin\\core.pyi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\core.pyi',
   'DATA'),
  ('pypinyin\\phonetic_symbol.pyi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\phonetic_symbol.pyi',
   'DATA'),
  ('pypinyin\\exceptions.pyi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\exceptions.pyi',
   'DATA'),
  ('pypinyin\\contrib\\mmseg.pyi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\contrib\\mmseg.pyi',
   'DATA'),
  ('pypinyin\\contrib\\neutral_tone.pyi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\contrib\\neutral_tone.pyi',
   'DATA'),
  ('pypinyin\\compat.pyi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\compat.pyi',
   'DATA'),
  ('pypinyin\\tools\\toneconvert.pyi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\tools\\toneconvert.pyi',
   'DATA'),
  ('pypinyin\\style\\wadegiles.pyi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\style\\wadegiles.pyi',
   'DATA'),
  ('pypinyin\\seg\\simpleseg.pyi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pypinyin\\seg\\simpleseg.pyi',
   'DATA'),
  ('dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coyhaique',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Coyhaique',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\zonenow.tab',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_table.tpl',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\formats\\templates\\html_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_table.tpl',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\formats\\templates\\latex_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex.tpl',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\formats\\templates\\latex.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html.tpl',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\formats\\templates\\html.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\string.tpl',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\formats\\templates\\string.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_style.tpl',
   'D:\\anaconda3\\envs\\win7\\lib\\site-packages\\pandas\\io\\formats\\templates\\html_style.tpl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tk_data\\dialog.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tk_data\\license.terms',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\MST',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tk_data\\menu.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\menu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tcl_data\\history.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tk_data\\msgs\\da.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\tzdata\\GB',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\GB',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tk_data\\msgs\\de.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\tzdata\\CET',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.14.tm',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8\\8.4\\platform-1.0.14.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tk_data\\text.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\text.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\parray.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\parray.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tk_data\\icons.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\icons.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tcl_data\\tzdata\\WET',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\init.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\init.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tk_data\\iconlist.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\package.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tk_data\\listbox.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tk_data\\obsolete.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tk_data\\palette.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tk_data\\bgerror.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.0.tm',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8\\8.5\\tcltest-2.5.0.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tk_data\\scrlbar.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tk_data\\optMenu.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\word.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\word.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tk_data\\entry.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\entry.tcl',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tk_data\\images\\README',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tk_data\\spinbox.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\East-Saskatchewan',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Canada\\East-Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tk_data\\safetk.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tk_data\\msgs\\el.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\tclIndex',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tclIndex',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tk_data\\msgs\\it.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tk_data\\choosedir.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tcl_data\\clock.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\clock.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tk_data\\tk.tcl', 'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\tk.tcl', 'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tcl_data\\tm.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tm.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tk_data\\msgs\\en.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tk_data\\focus.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\focus.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tk_data\\comdlg.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tk_data\\msgs\\es.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\EST',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\tzdata\\MET',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tk_data\\xmfbox.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\EET',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific-New',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\US\\Pacific-New',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tk_data\\button.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tk_data\\scale.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\scale.tcl',
   'DATA'),
  ('_tk_data\\console.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\auto.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\auto.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tk_data\\tearoff.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.0.tm',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8\\8.6\\http-2.9.0.tm',
   'DATA'),
  ('_tk_data\\msgbox.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tk_data\\tkfbox.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\safe.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\safe.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tk_data\\tclIndex',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tk_data\\clrpick.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'D:\\anaconda3\\envs\\win7\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\HST',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'D:\\anaconda3\\envs\\win7\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('base_library.zip',
   'E:\\code\\yiyaoxinxichuli\\build\\Medical_System\\base_library.zip',
   'DATA')],
 [('stat', 'D:\\anaconda3\\envs\\win7\\lib\\stat.py', 'PYMODULE'),
  ('weakref', 'D:\\anaconda3\\envs\\win7\\lib\\weakref.py', 'PYMODULE'),
  ('types', 'D:\\anaconda3\\envs\\win7\\lib\\types.py', 'PYMODULE'),
  ('heapq', 'D:\\anaconda3\\envs\\win7\\lib\\heapq.py', 'PYMODULE'),
  ('sre_parse', 'D:\\anaconda3\\envs\\win7\\lib\\sre_parse.py', 'PYMODULE'),
  ('enum', 'D:\\anaconda3\\envs\\win7\\lib\\enum.py', 'PYMODULE'),
  ('abc', 'D:\\anaconda3\\envs\\win7\\lib\\abc.py', 'PYMODULE'),
  ('linecache', 'D:\\anaconda3\\envs\\win7\\lib\\linecache.py', 'PYMODULE'),
  ('copyreg', 'D:\\anaconda3\\envs\\win7\\lib\\copyreg.py', 'PYMODULE'),
  ('collections.abc',
   'D:\\anaconda3\\envs\\win7\\lib\\collections\\abc.py',
   'PYMODULE'),
  ('collections',
   'D:\\anaconda3\\envs\\win7\\lib\\collections\\__init__.py',
   'PYMODULE'),
  ('_bootlocale', 'D:\\anaconda3\\envs\\win7\\lib\\_bootlocale.py', 'PYMODULE'),
  ('warnings', 'D:\\anaconda3\\envs\\win7\\lib\\warnings.py', 'PYMODULE'),
  ('keyword', 'D:\\anaconda3\\envs\\win7\\lib\\keyword.py', 'PYMODULE'),
  ('operator', 'D:\\anaconda3\\envs\\win7\\lib\\operator.py', 'PYMODULE'),
  ('_collections_abc',
   'D:\\anaconda3\\envs\\win7\\lib\\_collections_abc.py',
   'PYMODULE'),
  ('posixpath', 'D:\\anaconda3\\envs\\win7\\lib\\posixpath.py', 'PYMODULE'),
  ('ntpath', 'D:\\anaconda3\\envs\\win7\\lib\\ntpath.py', 'PYMODULE'),
  ('reprlib', 'D:\\anaconda3\\envs\\win7\\lib\\reprlib.py', 'PYMODULE'),
  ('functools', 'D:\\anaconda3\\envs\\win7\\lib\\functools.py', 'PYMODULE'),
  ('encodings.zlib_codec',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_centeuro',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\mac_centeuro.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\idna.py',
   'PYMODULE'),
  ('encodings.hz',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'D:\\anaconda3\\envs\\win7\\lib\\encodings\\__init__.py',
   'PYMODULE'),
  ('traceback', 'D:\\anaconda3\\envs\\win7\\lib\\traceback.py', 'PYMODULE'),
  ('io', 'D:\\anaconda3\\envs\\win7\\lib\\io.py', 'PYMODULE'),
  ('sre_constants',
   'D:\\anaconda3\\envs\\win7\\lib\\sre_constants.py',
   'PYMODULE'),
  ('sre_compile', 'D:\\anaconda3\\envs\\win7\\lib\\sre_compile.py', 'PYMODULE'),
  ('locale', 'D:\\anaconda3\\envs\\win7\\lib\\locale.py', 'PYMODULE'),
  ('_weakrefset', 'D:\\anaconda3\\envs\\win7\\lib\\_weakrefset.py', 'PYMODULE'),
  ('genericpath', 'D:\\anaconda3\\envs\\win7\\lib\\genericpath.py', 'PYMODULE'),
  ('codecs', 'D:\\anaconda3\\envs\\win7\\lib\\codecs.py', 'PYMODULE'),
  ('re', 'D:\\anaconda3\\envs\\win7\\lib\\re.py', 'PYMODULE'),
  ('os', 'D:\\anaconda3\\envs\\win7\\lib\\os.py', 'PYMODULE')])
